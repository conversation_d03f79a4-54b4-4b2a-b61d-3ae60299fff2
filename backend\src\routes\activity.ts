import { Router } from 'express';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { activityService } from '@/services/activityService';
import { ActivityType } from '@/types/shared';

const router = Router();

/**
 * Get user activity log with pagination
 * GET /api/activity
 */
router.get('/', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented

  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const type = req.query.type as ActivityType;

  const result = await activityService.getUserActivities(userId, page, limit, type);

  res.json({
    success: true,
    data: result
  });
}));

/**
 * Get recent activities for dashboard
 * GET /api/activity/recent
 */
router.get('/recent', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented

  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

  const activities = await activityService.getRecentActivities(userId, limit);

  res.json({
    success: true,
    data: activities
  });
}));

/**
 * Get activity statistics
 * GET /api/activity/stats
 */
router.get('/stats', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented

  const days = Math.min(parseInt(req.query.days as string) || 30, 365);

  const stats = await activityService.getActivityStats(userId, days);

  res.json({
    success: true,
    data: stats
  });
}));

export default router;
