import { useQuery } from '@tanstack/react-query'
import { activityApi } from '@/services/api'
import { ActivityType } from '@youtube-manager/shared'

export function useRecentActivity(limit: number = 10) {
  return useQuery({
    queryKey: ['recent-activity', limit],
    queryFn: async () => {
      const response = await fetch(`/api/activity/recent?limit=${limit}`)
      if (!response.ok) {
        throw new Error('Failed to fetch recent activity')
      }
      return response.json()
    },
    select: (data) => data.data,
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 15000, // Consider data stale after 15 seconds
  })
}

export function useActivityLog(
  page: number = 1,
  limit: number = 20,
  type?: ActivityType
) {
  return useQuery({
    queryKey: ['activity-log', page, limit, type],
    queryFn: () => activityApi.getAll(page, limit),
    keepPreviousData: true,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useActivityStats(days: number = 30) {
  return useQuery({
    queryKey: ['activity-stats', days],
    queryFn: async () => {
      const response = await fetch(`/api/activity/stats?days=${days}`)
      if (!response.ok) {
        throw new Error('Failed to fetch activity stats')
      }
      return response.json()
    },
    select: (data) => data.data,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
