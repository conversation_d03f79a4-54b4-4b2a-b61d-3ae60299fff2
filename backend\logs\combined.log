{
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:31:07',
  message: 'Database Info: Starting a postgresql pool with 13 connections.',
  target: 'quaint::pooled',
  level: 'info'
}
{
  service: 'youtube-manager-backend',
  name: 'PrismaClientInitializationError',
  clientVersion: '5.22.0',
  errorCode: 'P1001',
  level: 'error',
  message: "Failed to connect to database: Can't reach database server at `localhost:5432`\n" +
    '\n' +
    'Please make sure your database server is running at `localhost:5432`.',
  stack: "PrismaClientInitializationError: Can't reach database server at `localhost:5432`\n" +
    '\n' +
    'Please make sure your database server is running at `localhost:5432`.\n' +
    '    at t (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n' +
    '    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\utils\\database.ts:50:5)\n' +
    '    at async startServer (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\index.ts:99:5)',
  timestamp: '2025-06-18 00:31:11'
}
{
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07',
  message: 'Database Info: Starting a sqlite pool with 13 connections.',
  target: 'quaint::pooled',
  level: 'info'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Redis URL not provided, skipping Redis connection',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Starting cache warmup...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Cache warmup completed',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Starting background jobs...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Background jobs scheduled successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Background jobs started',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Server running on port 3001 in development mode',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: 'Connected clients: 0',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:07'
}
{
  message: '::1 - - [17/Jun/2025:23:34:32 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4202"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:34:32'
}
{
  message: '::1 - - [17/Jun/2025:23:35:12 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:35:12'
}
{
  message: '::1 - - [17/Jun/2025:23:35:13 +0000] "GET /favicon.ico HTTP/1.1" 404 79 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:35:13'
}
{
  message: '::1 - - [17/Jun/2025:23:37:16 +0000] "GET / HTTP/1.1" 404 68 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:37:16'
}
{
  message: '::1 - - [17/Jun/2025:23:37:32 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:37:32'
}
{
  message: '::1 - - [17/Jun/2025:23:40:14 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:14'
}
{
  message: '::1 - - [17/Jun/2025:23:40:15 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:15'
}
{
  message: '::1 - - [17/Jun/2025:23:40:18 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:18'
}
{
  message: '::1 - - [17/Jun/2025:23:40:19 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:19'
}
{
  message: '::1 - - [17/Jun/2025:23:40:19 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:19'
}
{
  message: '::1 - - [17/Jun/2025:23:40:19 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:19'
}
{
  message: '::1 - - [17/Jun/2025:23:40:19 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:19'
}
{
  message: '::1 - - [17/Jun/2025:23:40:19 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:19'
}
{
  message: '::1 - - [17/Jun/2025:23:40:19 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:19'
}
{
  message: '::1 - - [17/Jun/2025:23:40:20 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:20'
}
{
  message: '::1 - - [17/Jun/2025:23:40:20 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:20'
}
{
  message: '::1 - - [17/Jun/2025:23:40:20 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:20'
}
{
  message: '::1 - - [17/Jun/2025:23:40:20 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:20'
}
{
  message: '::1 - - [17/Jun/2025:23:40:20 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:20'
}
{
  message: '::1 - - [17/Jun/2025:23:40:21 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:21'
}
{
  message: '::1 - - [17/Jun/2025:23:40:21 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:21'
}
{
  message: '::1 - - [17/Jun/2025:23:40:21 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:21'
}
{
  message: '::1 - - [17/Jun/2025:23:40:21 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:21'
}
{
  message: '::1 - - [17/Jun/2025:23:40:21 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:21'
}
{
  message: '::1 - - [17/Jun/2025:23:40:22 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:22'
}
{
  message: '::1 - - [17/Jun/2025:23:40:22 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:22'
}
{
  message: '::1 - - [17/Jun/2025:23:40:22 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:22'
}
{
  message: '::1 - - [17/Jun/2025:23:40:22 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:22'
}
{
  message: '::1 - - [17/Jun/2025:23:40:22 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:22'
}
{
  message: '::1 - - [17/Jun/2025:23:40:22 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:22'
}
{
  message: '::1 - - [17/Jun/2025:23:40:23 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:23'
}
{
  message: '::1 - - [17/Jun/2025:23:40:23 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:23'
}
{
  message: '::1 - - [17/Jun/2025:23:40:23 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:23'
}
{
  message: '::1 - - [17/Jun/2025:23:40:23 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:23'
}
{
  message: '::1 - - [17/Jun/2025:23:40:23 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:23'
}
{
  message: '::1 - - [17/Jun/2025:23:40:23 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:23'
}
{
  message: '::1 - - [17/Jun/2025:23:40:24 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:24'
}
{
  message: '::1 - - [17/Jun/2025:23:40:24 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:24'
}
{
  message: '::1 - - [17/Jun/2025:23:40:24 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:24'
}
{
  message: '::1 - - [17/Jun/2025:23:40:24 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:24'
}
{
  message: '::1 - - [17/Jun/2025:23:40:24 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:24'
}
{
  message: '::1 - - [17/Jun/2025:23:40:25 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:25'
}
{
  message: '::1 - - [17/Jun/2025:23:40:25 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:25'
}
{
  message: '::1 - - [17/Jun/2025:23:40:25 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:25'
}
{
  message: '::1 - - [17/Jun/2025:23:40:25 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:25'
}
{
  message: '::1 - - [17/Jun/2025:23:40:25 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:25'
}
{
  message: '::1 - - [17/Jun/2025:23:40:25 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:25'
}
{
  message: '::1 - - [17/Jun/2025:23:40:26 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:26'
}
{
  message: '::1 - - [17/Jun/2025:23:40:26 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:26'
}
{
  message: '::1 - - [17/Jun/2025:23:40:26 +0000] "GET /health HTTP/1.1" 200 103 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:40:26'
}
{
  message: '::1 - - [17/Jun/2025:23:41:32 +0000] "GET /health HTTP/1.1" 200 103 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:41:32'
}
{
  message: '::1 - - [17/Jun/2025:23:42:36 +0000] "GET /health HTTP/1.1" 200 103 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:42:36'
}
{
  message: '::1 - - [17/Jun/2025:23:43:11 +0000] "GET / HTTP/1.1" 404 68 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:43:11'
}
{
  message: '::1 - - [17/Jun/2025:23:43:24 +0000] "GET /get HTTP/1.1" 404 71 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:43:24'
}
{
  message: '::1 - - [17/Jun/2025:23:53:49 +0000] "GET /health HTTP/1.1" 200 104 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4202"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:53:49'
}
{
  message: '::1 - - [17/Jun/2025:23:55:34 +0000] "GET /health HTTP/1.1" 200 104 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:55:34'
}
{
  message: '::1 - - [17/Jun/2025:23:55:59 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:55:59'
}
{
  message: '::1 - - [17/Jun/2025:23:56:11 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:56:11'
}
{
  message: '::1 - - [17/Jun/2025:23:56:13 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:56:13'
}
{
  message: '::1 - - [17/Jun/2025:23:56:37 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:56:37'
}
{
  message: '::1 - - [17/Jun/2025:23:56:41 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:56:41'
}
{
  message: '::1 - - [17/Jun/2025:23:56:57 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:56:57'
}
{
  message: '::1 - - [17/Jun/2025:23:57:12 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:57:12'
}
{
  message: '::1 - - [17/Jun/2025:23:57:15 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:57:15'
}
{
  message: '::1 - - [17/Jun/2025:23:57:57 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:57:57'
}
{
  message: '::1 - - [17/Jun/2025:23:57:58 +0000] "GET /api/youtube/status HTTP/1.1" 404 86 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:57:58'
}
{
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17',
  message: 'Database Info: Starting a sqlite pool with 13 connections.',
  target: 'quaint::pooled',
  level: 'info'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Redis URL not provided, skipping Redis connection',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Starting cache warmup...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Cache warmup completed',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Starting background jobs...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Background jobs scheduled successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Background jobs started',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Server running on port 3001 in development mode',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: 'Connected clients: 0',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:17'
}
{
  message: '::1 - - [17/Jun/2025:23:59:22 +0000] "GET /api/youtube/status HTTP/1.1" 200 70 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:22'
}
{
  message: '::1 - - [17/Jun/2025:23:59:24 +0000] "GET /api/channel-lists HTTP/1.1" 200 26 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:24'
}
{
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32',
  message: 'Database Info: Starting a sqlite pool with 13 connections.',
  target: 'quaint::pooled',
  level: 'info'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Redis URL not provided, skipping Redis connection',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Starting cache warmup...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Cache warmup completed',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Starting background jobs...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Background jobs scheduled successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Background jobs started',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Server running on port 3001 in development mode',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: 'Connected clients: 0',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  message: '::ffff:127.0.0.1 - - [17/Jun/2025:23:59:32 +0000] "GET /api/youtube/status HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 00:59:32'
}
{
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05',
  message: 'Database Info: Starting a sqlite pool with 13 connections.',
  target: 'quaint::pooled',
  level: 'info'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Redis URL not provided, skipping Redis connection',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Starting cache warmup...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Cache warmup completed',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Starting background jobs...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Background jobs scheduled successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Background jobs started',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Server running on port 3001 in development mode',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  message: 'Connected clients: 0',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:05'
}
{
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36',
  message: 'Database Info: Starting a sqlite pool with 13 connections.',
  target: 'quaint::pooled',
  level: 'info'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Redis URL not provided, skipping Redis connection',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Starting cache warmup...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Cache warmup completed',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Starting background jobs...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Background jobs scheduled successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Background jobs started',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Server running on port 3001 in development mode',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  message: 'Connected clients: 0',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:36'
}
{
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53',
  message: 'Database Info: Starting a sqlite pool with 13 connections.',
  target: 'quaint::pooled',
  level: 'info'
}
{
  message: 'Database connection established successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Redis URL not provided, skipping Redis connection',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Real-time service initialized',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Starting cache warmup...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Cache warmup completed',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Starting background jobs...',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Background jobs scheduled successfully',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Background jobs started',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Server running on port 3001 in development mode',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: 'Connected clients: 0',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:53'
}
{
  message: '::1 - - [18/Jun/2025:00:00:54 +0000] "GET /api/youtube/status HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:00:54'
}
{
  message: '::1 - - [18/Jun/2025:00:01:01 +0000] "GET /health HTTP/1.1" 200 102 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4202"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:01'
}
{
  message: '::1 - - [18/Jun/2025:00:01:10 +0000] "GET /api/youtube/status HTTP/1.1" 200 70 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4202"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:10'
}
{
  message: 'Retrieved 5 channel details from YouTube API',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:21'
}
{
  message: 'YouTube search completed: 5 channels found',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:21'
}
{
  message: '::1 - - [18/Jun/2025:00:01:21 +0000] "GET /api/youtube/search?query=tech&limit=5 HTTP/1.1" 200 3633 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4202"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:21'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204882100,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:01:33 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:01:22',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
{
  message: '::1 - - [18/Jun/2025:00:01:22 +0000] "POST /api/youtube/configure HTTP/1.1" 400 64 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:22'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204896090,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:01:47 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:01:36',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
{
  message: '::1 - - [18/Jun/2025:00:01:36 +0000] "POST /api/youtube/configure HTTP/1.1" 400 64 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:36'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204914492,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:02:05 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:01:54',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
{
  message: '::1 - - [18/Jun/2025:00:01:54 +0000] "POST /api/youtube/configure HTTP/1.1" 400 64 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:01:54'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204952283,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:02:43 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:02:32',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
{
  message: '::1 - - [18/Jun/2025:00:02:32 +0000] "POST /api/youtube/configure HTTP/1.1" 400 64 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'youtube-manager-backend',
  timestamp: '2025-06-18 01:02:32'
}
