import { prisma } from '@/utils/database';
import { logger } from '@/utils/logger';
import { ActivityType } from '@/types/shared';

export interface ActivityMetadata {
  channelId?: string;
  channelTitle?: string;
  listId?: string;
  listName?: string;
  oldValue?: string;
  newValue?: string;
  [key: string]: any;
}

export class ActivityService {
  /**
   * Log a user activity
   */
  async logActivity(
    userId: string,
    type: ActivityType,
    description: string,
    metadata?: ActivityMetadata
  ) {
    try {
      const activity = await prisma.activityLog.create({
        data: {
          userId,
          type,
          description,
          metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
        },
      });

      logger.info('Activity logged', {
        userId,
        type,
        description,
        activityId: activity.id,
      });

      return activity;
    } catch (error) {
      logger.error('Failed to log activity', {
        userId,
        type,
        description,
        error,
      });
      // Don't throw error to avoid breaking main functionality
    }
  }

  /**
   * Log channel added to list activity
   */
  async logChannelAdded(
    userId: string,
    channelTitle: string,
    listName: string,
    metadata?: Partial<ActivityMetadata>
  ) {
    return this.logActivity(
      userId,
      ActivityType.CHANNEL_ADDED,
      `Added "${channelTitle}" to "${listName}"`,
      {
        channelTitle,
        listName,
        ...metadata,
      }
    );
  }

  /**
   * Log channel removed from list activity
   */
  async logChannelRemoved(
    userId: string,
    channelTitle: string,
    listName: string,
    metadata?: Partial<ActivityMetadata>
  ) {
    return this.logActivity(
      userId,
      ActivityType.CHANNEL_REMOVED,
      `Removed "${channelTitle}" from "${listName}"`,
      {
        channelTitle,
        listName,
        ...metadata,
      }
    );
  }

  /**
   * Log list created activity
   */
  async logListCreated(
    userId: string,
    listName: string,
    metadata?: Partial<ActivityMetadata>
  ) {
    return this.logActivity(
      userId,
      ActivityType.LIST_CREATED,
      `Created list "${listName}"`,
      {
        listName,
        ...metadata,
      }
    );
  }

  /**
   * Log list updated activity
   */
  async logListUpdated(
    userId: string,
    listName: string,
    changes: string[],
    metadata?: Partial<ActivityMetadata>
  ) {
    const changeDescription = changes.join(', ');
    return this.logActivity(
      userId,
      ActivityType.LIST_UPDATED,
      `Updated "${listName}": ${changeDescription}`,
      {
        listName,
        changes,
        ...metadata,
      }
    );
  }

  /**
   * Log list renamed activity
   */
  async logListRenamed(
    userId: string,
    oldName: string,
    newName: string,
    metadata?: Partial<ActivityMetadata>
  ) {
    return this.logActivity(
      userId,
      ActivityType.LIST_RENAMED,
      `Renamed list from "${oldName}" to "${newName}"`,
      {
        oldValue: oldName,
        newValue: newName,
        listName: newName,
        ...metadata,
      }
    );
  }

  /**
   * Log list deleted activity
   */
  async logListDeleted(
    userId: string,
    listName: string,
    channelCount: number,
    metadata?: Partial<ActivityMetadata>
  ) {
    return this.logActivity(
      userId,
      ActivityType.LIST_DELETED,
      `Deleted list "${listName}" (${channelCount} channels)`,
      {
        listName,
        channelCount,
        ...metadata,
      }
    );
  }

  /**
   * Log channel moved between lists activity
   */
  async logChannelMoved(
    userId: string,
    channelTitle: string,
    fromList: string,
    toList: string,
    metadata?: Partial<ActivityMetadata>
  ) {
    return this.logActivity(
      userId,
      ActivityType.CHANNEL_MOVED,
      `Moved "${channelTitle}" from "${fromList}" to "${toList}"`,
      {
        channelTitle,
        oldValue: fromList,
        newValue: toList,
        ...metadata,
      }
    );
  }

  /**
   * Get user activities with pagination
   */
  async getUserActivities(
    userId: string,
    page: number = 1,
    limit: number = 20,
    type?: ActivityType
  ) {
    const skip = (page - 1) * limit;

    const where = {
      userId,
      ...(type && { type }),
    };

    const [activities, total] = await Promise.all([
      prisma.activityLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.activityLog.count({ where }),
    ]);

    return {
      data: activities,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get recent activities for dashboard
   */
  async getRecentActivities(userId: string, limit: number = 10) {
    return prisma.activityLog.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  /**
   * Clean up old activities (older than specified days)
   */
  async cleanupOldActivities(daysToKeep: number = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    try {
      const result = await prisma.activityLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      logger.info(`Cleaned up ${result.count} old activity logs`);
      return result.count;
    } catch (error) {
      logger.error('Failed to cleanup old activities', error);
      throw error;
    }
  }

  /**
   * Get activity statistics
   */
  async getActivityStats(userId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const activities = await prisma.activityLog.findMany({
      where: {
        userId,
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        type: true,
        createdAt: true,
      },
    });

    // Group by type
    const byType = activities.reduce((acc, activity) => {
      acc[activity.type] = (acc[activity.type] || 0) + 1;
      return acc;
    }, {} as Record<ActivityType, number>);

    // Group by day
    const byDay = activities.reduce((acc, activity) => {
      const day = activity.createdAt.toISOString().split('T')[0];
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: activities.length,
      byType,
      byDay,
      period: `${days} days`,
    };
  }
}

export const activityService = new ActivityService();
