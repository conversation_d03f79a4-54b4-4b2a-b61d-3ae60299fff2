import { MoreHorizontal, <PERSON>, Clock } from 'lucide-react'

interface Channel {
  id: string
  thumbnailUrl: string
  title: string
}

interface ChannelList {
  id: string
  name: string
  description?: string
  channelCount: number
  color: string
  lastUpdated: string
  featured?: boolean
  channels: Channel[]
}

interface ChannelListCardProps {
  list: ChannelList
  viewMode: 'grid' | 'list'
}

export function ChannelListCard({ list, viewMode }: ChannelListCardProps) {
  if (viewMode === 'list') {
    return (
      <div className="card flex items-center gap-6 p-4">
        <div 
          className="w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-xl"
          style={{ backgroundColor: list.color }}
        >
          {list.name.charAt(0)}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="text-lg font-semibold text-white">{list.name}</h3>
            {list.featured && (
              <span className="px-2 py-1 bg-primary-600 text-white text-xs rounded-full">
                Featured
              </span>
            )}
          </div>
          {list.description && (
            <p className="text-slate-400 text-sm mb-2">{list.description}</p>
          )}
          <div className="flex items-center gap-4 text-sm text-slate-400">
            <div className="flex items-center gap-1">
              <Users className="w-4 h-4" />
              <span>{list.channelCount} channels</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>Updated {list.lastUpdated}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {list.channels.slice(0, 4).map((channel) => (
            <img
              key={channel.id}
              src={channel.thumbnailUrl}
              alt={channel.title}
              className="w-8 h-8 rounded-full"
            />
          ))}
          {list.channels.length > 4 && (
            <div className="w-8 h-8 bg-dark-700 rounded-full flex items-center justify-center text-xs text-slate-400">
              +{list.channels.length - 4}
            </div>
          )}
        </div>

        <button className="p-2 text-slate-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors">
          <MoreHorizontal className="w-5 h-5" />
        </button>
      </div>
    )
  }

  return (
    <div 
      className={`card hover:border-opacity-50 transition-all cursor-pointer ${
        list.featured ? 'ring-2 ring-primary-500 ring-opacity-50' : ''
      }`}
      style={{ borderColor: list.featured ? list.color : undefined }}
    >
      <div className="flex items-start justify-between mb-4">
        <div 
          className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold"
          style={{ backgroundColor: list.color }}
        >
          {list.name.charAt(0)}
        </div>
        <button className="p-1 text-slate-400 hover:text-white hover:bg-dark-700 rounded transition-colors">
          <MoreHorizontal className="w-4 h-4" />
        </button>
      </div>

      <div className="mb-4">
        <div className="flex items-center gap-2 mb-1">
          <h3 className="font-semibold text-white">{list.name}</h3>
          {list.featured && (
            <span className="px-2 py-1 bg-primary-600 text-white text-xs rounded-full">
              Featured
            </span>
          )}
        </div>
        {list.description && (
          <p className="text-slate-400 text-sm">{list.description}</p>
        )}
      </div>

      <div className="flex items-center gap-4 text-sm text-slate-400 mb-4">
        <div className="flex items-center gap-1">
          <Users className="w-4 h-4" />
          <span>{list.channelCount} channels</span>
        </div>
        <div className="flex items-center gap-1">
          <Clock className="w-4 h-4" />
          <span>Updated {list.lastUpdated}</span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex -space-x-2">
          {list.channels.slice(0, 4).map((channel) => (
            <img
              key={channel.id}
              src={channel.thumbnailUrl}
              alt={channel.title}
              className="w-8 h-8 rounded-full border-2 border-dark-800"
            />
          ))}
          {list.channels.length > 4 && (
            <div className="w-8 h-8 bg-dark-700 rounded-full border-2 border-dark-800 flex items-center justify-center text-xs text-slate-400">
              +{list.channels.length - 4}
            </div>
          )}
        </div>
        
        <button 
          className="text-primary-400 hover:text-primary-300 text-sm font-medium"
        >
          View
        </button>
      </div>
    </div>
  )
}
