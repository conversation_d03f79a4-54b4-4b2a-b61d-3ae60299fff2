import { Router } from 'express';
import { asyncHand<PERSON>, createError } from '@/middleware/errorHandler';
import { prisma } from '@/utils/database';
import { activityService } from '@/services/activityService';
import { ErrorCode } from '@youtube-manager/shared';

const router = Router();

/**
 * Get all channel lists for user
 * GET /api/channel-lists
 */
router.get('/', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented

  const channelLists = await prisma.channelList.findMany({
    where: { userId },
    include: {
      channelItems: {
        include: {
          channel: true
        }
      }
    },
    orderBy: { updatedAt: 'desc' }
  });

  // Transform data to include channel count and preview channels
  const transformedLists = channelLists.map(list => ({
    id: list.id,
    userId: list.userId,
    name: list.name,
    description: list.description,
    color: list.color,
    isPublic: list.isPublic,
    channelCount: list.channelItems.length,
    createdAt: list.createdAt,
    updatedAt: list.updatedAt,
    channels: list.channelItems.slice(0, 4).map(item => ({
      id: item.channel.id,
      channelId: item.channel.channelId,
      title: item.channel.title,
      thumbnailUrl: item.channel.thumbnailUrl
    }))
  }));

  res.json({
    success: true,
    data: transformedLists
  });
}));

/**
 * Create new channel list
 * POST /api/channel-lists
 */
router.post('/', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented

  const { name, description, color = '#8B5CF6', isPublic = false } = req.body;

  if (!name || name.trim().length === 0) {
    throw createError('List name is required', 400, ErrorCode.VALIDATION_ERROR);
  }

  if (name.length > 100) {
    throw createError('List name must be 100 characters or less', 400, ErrorCode.VALIDATION_ERROR);
  }

  const channelList = await prisma.channelList.create({
    data: {
      userId,
      name: name.trim(),
      description: description?.trim() || null,
      color,
      isPublic,
      channelCount: 0
    }
  });

  // Log activity
  await activityService.logListCreated(userId, channelList.name, {
    listId: channelList.id,
    color: channelList.color
  });

  res.status(201).json({
    success: true,
    data: channelList
  });
}));

/**
 * Update channel list
 * PUT /api/channel-lists/:listId
 */
router.put('/:listId', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented
  const { listId } = req.params;
  const { name, description, color, isPublic } = req.body;

  // Find existing list
  const existingList = await prisma.channelList.findFirst({
    where: { id: listId, userId }
  });

  if (!existingList) {
    throw createError('Channel list not found', 404, ErrorCode.LIST_NOT_FOUND);
  }

  // Prepare update data
  const updateData: any = {};
  const changes: string[] = [];

  if (name !== undefined && name !== existingList.name) {
    if (!name || name.trim().length === 0) {
      throw createError('List name is required', 400, ErrorCode.VALIDATION_ERROR);
    }
    if (name.length > 100) {
      throw createError('List name must be 100 characters or less', 400, ErrorCode.VALIDATION_ERROR);
    }
    updateData.name = name.trim();
    changes.push('name');
  }

  if (description !== undefined && description !== existingList.description) {
    updateData.description = description?.trim() || null;
    changes.push('description');
  }

  if (color !== undefined && color !== existingList.color) {
    updateData.color = color;
    changes.push('color');
  }

  if (isPublic !== undefined && isPublic !== existingList.isPublic) {
    updateData.isPublic = isPublic;
    changes.push(isPublic ? 'made public' : 'made private');
  }

  if (Object.keys(updateData).length === 0) {
    return res.json({
      success: true,
      data: existingList
    });
  }

  const updatedList = await prisma.channelList.update({
    where: { id: listId },
    data: updateData
  });

  // Log activity
  if (name !== undefined && name !== existingList.name) {
    await activityService.logListRenamed(userId, existingList.name, updatedList.name, {
      listId: updatedList.id
    });
  } else if (changes.length > 0) {
    await activityService.logListUpdated(userId, updatedList.name, changes, {
      listId: updatedList.id
    });
  }

  res.json({
    success: true,
    data: updatedList
  });
}));

/**
 * Delete channel list
 * DELETE /api/channel-lists/:listId
 */
router.delete('/:listId', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented
  const { listId } = req.params;

  // Find existing list with channel count
  const existingList = await prisma.channelList.findFirst({
    where: { id: listId, userId },
    include: {
      _count: {
        select: { channelItems: true }
      }
    }
  });

  if (!existingList) {
    throw createError('Channel list not found', 404, ErrorCode.LIST_NOT_FOUND);
  }

  // Delete the list (cascade will handle channel items)
  await prisma.channelList.delete({
    where: { id: listId }
  });

  // Log activity
  await activityService.logListDeleted(
    userId,
    existingList.name,
    existingList._count.channelItems,
    { listId }
  );

  res.json({
    success: true,
    message: 'Channel list deleted successfully'
  });
}));

/**
 * Add channel to list
 * POST /api/channel-lists/:listId/channels
 */
router.post('/:listId/channels', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented
  const { listId } = req.params;
  const { channelId } = req.body;

  if (!channelId) {
    throw createError('Channel ID is required', 400, ErrorCode.VALIDATION_ERROR);
  }

  // Verify list exists and belongs to user
  const channelList = await prisma.channelList.findFirst({
    where: { id: listId, userId }
  });

  if (!channelList) {
    throw createError('Channel list not found', 404, ErrorCode.LIST_NOT_FOUND);
  }

  // Check if channel exists in our database
  let channel = await prisma.youTubeChannel.findUnique({
    where: { id: channelId }
  });

  if (!channel) {
    throw createError('Channel not found', 404, ErrorCode.CHANNEL_NOT_FOUND);
  }

  // Check if channel is already in the list
  const existingItem = await prisma.channelListItem.findUnique({
    where: {
      channelListId_channelId: {
        channelListId: listId,
        channelId: channelId
      }
    }
  });

  if (existingItem) {
    return res.json({
      success: true,
      message: 'Channel is already in the list'
    });
  }

  // Add channel to list
  await prisma.channelListItem.create({
    data: {
      channelListId: listId,
      channelId: channelId
    }
  });

  // Update channel count
  await prisma.channelList.update({
    where: { id: listId },
    data: {
      channelCount: {
        increment: 1
      }
    }
  });

  // Log activity
  await activityService.logChannelAdded(userId, channel.title, channelList.name, {
    channelId: channel.id,
    listId: listId
  });

  res.json({
    success: true,
    message: 'Channel added to list successfully'
  });
}));

/**
 * Remove channel from list
 * DELETE /api/channel-lists/:listId/channels/:channelId
 */
router.delete('/:listId/channels/:channelId', asyncHandler(async (req, res) => {
  // TODO: Get userId from auth middleware
  const userId = 'temp-user-id'; // Placeholder until auth is implemented
  const { listId, channelId } = req.params;

  // Verify list exists and belongs to user
  const channelList = await prisma.channelList.findFirst({
    where: { id: listId, userId }
  });

  if (!channelList) {
    throw createError('Channel list not found', 404, ErrorCode.LIST_NOT_FOUND);
  }

  // Get channel info for logging
  const channel = await prisma.youTubeChannel.findUnique({
    where: { id: channelId }
  });

  if (!channel) {
    throw createError('Channel not found', 404, ErrorCode.CHANNEL_NOT_FOUND);
  }

  // Check if channel is in the list
  const existingItem = await prisma.channelListItem.findUnique({
    where: {
      channelListId_channelId: {
        channelListId: listId,
        channelId: channelId
      }
    }
  });

  if (!existingItem) {
    return res.json({
      success: true,
      message: 'Channel is not in the list'
    });
  }

  // Remove channel from list
  await prisma.channelListItem.delete({
    where: {
      channelListId_channelId: {
        channelListId: listId,
        channelId: channelId
      }
    }
  });

  // Update channel count
  await prisma.channelList.update({
    where: { id: listId },
    data: {
      channelCount: {
        decrement: 1
      }
    }
  });

  // Log activity
  await activityService.logChannelRemoved(userId, channel.title, channelList.name, {
    channelId: channel.id,
    listId: listId
  });

  res.json({
    success: true,
    message: 'Channel removed from list successfully'
  });
}));

export default router;
