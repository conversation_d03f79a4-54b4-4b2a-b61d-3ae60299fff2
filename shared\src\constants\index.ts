// API Constants
export const API_ENDPOINTS = {
  CHANNELS: '/api/channels',
  CHANNEL_LISTS: '/api/channel-lists',
  YOUTUBE_SEARCH: '/api/youtube/search',
  YOUTUBE_CHANNEL: '/api/youtube/channel',
  ACTIVITY: '/api/activity',
  AUTH: '/api/auth'
} as const;

// YouTube API Constants
export const YOUTUBE_API = {
  BASE_URL: 'https://www.googleapis.com/youtube/v3',
  QUOTA_COSTS: {
    SEARCH: 100,
    CHANNELS: 1,
    VIDEOS: 1,
    PLAYLISTS: 1
  },
  DAILY_QUOTA_LIMIT: 10000,
  MAX_RESULTS_PER_REQUEST: 50
} as const;

// UI Constants
export const UI_CONSTANTS = {
  COLORS: {
    PRIMARY: '#8B5CF6',
    SECONDARY: '#06B6D4',
    SUCCESS: '#10B981',
    WARNING: '#F59E0B',
    ERROR: '#EF4444',
    INFO: '#6366F1'
  },
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536
  },
  GRID_COLUMNS: {
    MIN: 1,
    MAX: 6,
    DEFAULT: 3
  }
} as const;

// Cache Constants
export const CACHE_KEYS = {
  YOUTUBE_CHANNEL: 'youtube:channel:',
  YOUTUBE_SEARCH: 'youtube:search:',
  CHANNEL_LISTS: 'channel:lists:',
  USER_ACTIVITY: 'user:activity:'
} as const;

export const CACHE_TTL = {
  YOUTUBE_CHANNEL: 3600, // 1 hour
  YOUTUBE_SEARCH: 1800,  // 30 minutes
  CHANNEL_LISTS: 300,    // 5 minutes
  USER_ACTIVITY: 600     // 10 minutes
} as const;

// Validation Constants
export const VALIDATION = {
  CHANNEL_LIST_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 100
  },
  CHANNEL_LIST_DESCRIPTION: {
    MAX_LENGTH: 500
  },
  SEARCH_QUERY: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 200
  },
  SUBSCRIBER_COUNT: {
    MIN: 0,
    MAX: 1000000000 // 1 billion
  }
} as const;

// Default Values
export const DEFAULTS = {
  PAGINATION: {
    PAGE: 1,
    LIMIT: 20,
    MAX_LIMIT: 100
  },
  VIEW_MODE: {
    TYPE: 'grid' as const,
    COLUMNS: 3
  },
  SORT: {
    BY: 'updatedAt',
    ORDER: 'desc' as const
  }
} as const;
