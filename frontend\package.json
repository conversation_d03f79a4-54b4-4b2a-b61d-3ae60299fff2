{"name": "@youtube-manager/frontend", "version": "1.0.0", "description": "Frontend React application for YouTube Content Manager", "private": true, "type": "module", "scripts": {"dev": "npx vite", "build": "npx tsc && npx vite build", "preview": "npx vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.14.2", "axios": "^1.6.2", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.1.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.4.19"}, "engines": {"node": ">=18.0.0"}}