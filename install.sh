#!/bin/bash

# YouTube Content Manager - Installation Script
# This script helps set up the development environment

set -e

echo "🚀 YouTube Content Manager - Installation Script"
echo "================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed."
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
echo "=============================="

# Install root dependencies
echo "Installing root dependencies..."
npm install

# Install shared package dependencies
echo "Installing shared package dependencies..."
cd shared && npm install && cd ..

# Install backend dependencies
echo "Installing backend dependencies..."
cd backend && npm install && cd ..

# Install frontend dependencies
echo "Installing frontend dependencies..."
cd frontend && npm install && cd ..

# Build shared package
echo ""
echo "🔨 Building shared package..."
echo "=============================="
cd shared && npm run build && cd ..

# Create environment files
echo ""
echo "⚙️  Setting up environment files..."
echo "=================================="

# Backend environment
if [ ! -f "backend/.env" ]; then
    cp backend/.env.example backend/.env
    echo "✅ Created backend/.env from example"
    echo "⚠️  Please edit backend/.env with your configuration"
else
    echo "✅ backend/.env already exists"
fi

# Frontend environment
if [ ! -f "frontend/.env" ]; then
    cp frontend/.env.example frontend/.env
    echo "✅ Created frontend/.env from example"
else
    echo "✅ frontend/.env already exists"
fi

echo ""
echo "🎉 Installation completed successfully!"
echo "======================================"
echo ""
echo "Next steps:"
echo "1. Set up PostgreSQL database"
echo "2. Get YouTube Data API key from Google Cloud Console"
echo "3. Edit backend/.env with your database URL and API key"
echo "4. Run database migrations: cd backend && npm run db:push"
echo "5. Start the development servers: npm run dev"
echo ""
echo "For detailed setup instructions, see setup.md"
echo ""
echo "Happy coding! 🚀"
