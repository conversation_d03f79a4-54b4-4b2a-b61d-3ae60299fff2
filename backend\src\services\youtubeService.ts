import { google, youtube_v3 } from 'googleapis';
import { logger } from '@/utils/logger';
import { getCache, setCache } from '@/utils/redis';
import { prisma } from '@/utils/database';
import { createError } from '@/middleware/errorHandler';
import { <PERSON>rrorCode, YouTubeSearchFilters, YouTubeChannelStats, YOUTUBE_API, CACHE_KEYS, CACHE_TTL } from '@/types/shared';

export class YouTubeService {
  private youtube: youtube_v3.Youtube;
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.YOUTUBE_API_KEY || '';
    this.initializeYouTubeClient();
  }

  private initializeYouTubeClient() {
    if (this.apiKey) {
      this.youtube = google.youtube({
        version: 'v3',
        auth: this.apiKey
      });
    }
  }

  /**
   * Test if an API key is valid
   */
  async testApiKey(apiKey: string): Promise<boolean> {
    try {
      const testYoutube = google.youtube({
        version: 'v3',
        auth: apiKey
      });

      // Make a simple API call to test the key - use a known public channel
      const response = await testYoutube.channels.list({
        part: ['snippet'],
        id: ['UCuAXFkgsw1L7xaCfnd5JJOw'] // Random YouTube channel ID for testing
      });

      return response.status === 200 && response.data.items && response.data.items.length > 0;
    } catch (error: any) {
      logger.error('API key test failed:', error);
      return false;
    }
  }

  /**
   * Update the API key and reinitialize the client
   */
  updateApiKey(apiKey: string) {
    this.apiKey = apiKey;
    this.initializeYouTubeClient();
  }

  /**
   * Search for YouTube channels based on filters
   */
  async searchChannels(filters: YouTubeSearchFilters, maxResults: number = 25) {
    if (!this.apiKey) {
      throw createError('YouTube API key not configured', 400, ErrorCode.YOUTUBE_API_ERROR);
    }

    const cacheKey = `${CACHE_KEYS.YOUTUBE_SEARCH}${JSON.stringify(filters)}_${maxResults}`;

    try {
      // Check cache first
      const cachedResult = await getCache(cacheKey);
      if (cachedResult) {
        logger.debug('YouTube search cache hit');
        return cachedResult;
      }

      // Check quota before making API call
      await this.checkAndUpdateQuota(YOUTUBE_API.QUOTA_COSTS.SEARCH);

      const searchParams: youtube_v3.Params$Resource$Search$List = {
        part: ['snippet'],
        type: ['channel'],
        maxResults: Math.min(maxResults, YOUTUBE_API.MAX_RESULTS_PER_REQUEST),
        order: filters.order || 'relevance',
        q: filters.query || ''
      };

      if (filters.publishedAfter) {
        searchParams.publishedAfter = filters.publishedAfter.toISOString();
      }
      if (filters.publishedBefore) {
        searchParams.publishedBefore = filters.publishedBefore.toISOString();
      }
      if (filters.country) {
        searchParams.regionCode = filters.country;
      }

      const response = await this.youtube.search.list(searchParams);
      
      if (!response.data.items) {
        return { channels: [], totalResults: 0 };
      }

      // Get detailed channel information
      const channelIds = response.data.items
        .map(item => item.snippet?.channelId)
        .filter(Boolean) as string[];

      const channelsData = await this.getChannelsByIds(channelIds);

      // Apply subscriber and view filters
      const filteredChannels = Array.isArray(channelsData) ? channelsData.filter(channel => {
        if (filters.minSubscribers && channel.subscriberCount < filters.minSubscribers) {
          return false;
        }
        if (filters.maxSubscribers && channel.subscriberCount > filters.maxSubscribers) {
          return false;
        }
        if (filters.minViews && channel.viewCount < filters.minViews) {
          return false;
        }
        if (filters.maxViews && channel.viewCount > filters.maxViews) {
          return false;
        }
        return true;
      }) : [];

      const result = {
        channels: filteredChannels,
        totalResults: response.data.pageInfo?.totalResults || 0,
        nextPageToken: response.data.nextPageToken
      };

      // Cache the result
      await setCache(cacheKey, result, CACHE_TTL.YOUTUBE_SEARCH);

      logger.info(`YouTube search completed: ${filteredChannels.length} channels found`);
      return result;

    } catch (error: any) {
      logger.error('YouTube search error:', error);

      if (error.code === 403) {
        throw createError('YouTube API quota exceeded', 429, ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED);
      }
      
      throw createError('YouTube API search failed', 500, ErrorCode.YOUTUBE_API_ERROR);
    }
  }

  /**
   * Get detailed information for specific channel IDs
   */
  async getChannelsByIds(channelIds: string[]) {
    if (channelIds.length === 0) return [];

    const cacheKey = `${CACHE_KEYS.YOUTUBE_CHANNEL}${channelIds.sort().join(',')}`;
    
    try {
      // Check cache first
      const cachedResult = await getCache(cacheKey);
      if (cachedResult) {
        logger.debug('YouTube channels cache hit');
        return cachedResult;
      }

      // Check quota before making API call
      await this.checkAndUpdateQuota(YOUTUBE_API.QUOTA_COSTS.CHANNELS);

      const response = await this.youtube.channels.list({
        part: ['snippet', 'statistics', 'brandingSettings'],
        id: channelIds
      });

      if (!response.data.items) {
        return [];
      }

      const channels = response.data.items.map(item => ({
        channelId: item.id!,
        title: item.snippet?.title || '',
        description: item.snippet?.description || '',
        thumbnailUrl: item.snippet?.thumbnails?.high?.url || 
                     item.snippet?.thumbnails?.medium?.url || 
                     item.snippet?.thumbnails?.default?.url || '',
        subscriberCount: parseInt(item.statistics?.subscriberCount || '0'),
        videoCount: parseInt(item.statistics?.videoCount || '0'),
        viewCount: parseInt(item.statistics?.viewCount || '0'),
        publishedAt: new Date(item.snippet?.publishedAt || Date.now()),
        customUrl: item.snippet?.customUrl,
        country: item.snippet?.country,
        lastUpdated: new Date()
      }));

      // Cache the result
      await setCache(cacheKey, channels, CACHE_TTL.YOUTUBE_CHANNEL);

      logger.info(`Retrieved ${channels.length} channel details from YouTube API`);
      return channels;

    } catch (error: any) {
      logger.error('YouTube channels fetch error:', error);

      if (error.code === 403) {
        throw createError('YouTube API quota exceeded', 429, ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED);
      }
      
      throw createError('YouTube API channels fetch failed', 500, ErrorCode.YOUTUBE_API_ERROR);
    }
  }

  /**
   * Get a single channel by ID with caching
   */
  async getChannelById(channelId: string) {
    const channels = await this.getChannelsByIds([channelId]);
    return channels[0] || null;
  }

  /**
   * Get channel statistics and analytics
   */
  async getChannelStats(channelId: string): Promise<YouTubeChannelStats> {
    const cacheKey = `${CACHE_KEYS.YOUTUBE_CHANNEL}stats_${channelId}`;
    
    try {
      // Check cache first
      const cachedStats = await getCache<YouTubeChannelStats>(cacheKey);
      if (cachedStats) {
        logger.debug('YouTube channel stats cache hit');
        return cachedStats;
      }

      // Get basic channel info
      const channel = await this.getChannelById(channelId);
      if (!channel) {
        throw createError('Channel not found', 404, ErrorCode.CHANNEL_NOT_FOUND);
      }

      // Get recent videos to calculate upload frequency and average views
      const videosResponse = await this.youtube.search.list({
        part: ['snippet'],
        channelId: channelId,
        type: ['video'],
        order: 'date',
        maxResults: 10
      });

      let lastVideoDate: Date | undefined;
      let uploadFrequency = 'Unknown';

      if (videosResponse.data.items && videosResponse.data.items.length > 0) {
        const videos = videosResponse.data.items;
        lastVideoDate = new Date(videos[0].snippet?.publishedAt || Date.now());

        // Calculate upload frequency based on recent videos
        if (videos.length >= 2) {
          const firstVideo = new Date(videos[videos.length - 1].snippet?.publishedAt || Date.now());
          const daysDiff = Math.floor((lastVideoDate.getTime() - firstVideo.getTime()) / (1000 * 60 * 60 * 24));
          const avgDaysBetweenVideos = daysDiff / (videos.length - 1);

          if (avgDaysBetweenVideos <= 1) {
            uploadFrequency = 'Daily';
          } else if (avgDaysBetweenVideos <= 7) {
            uploadFrequency = 'Weekly';
          } else if (avgDaysBetweenVideos <= 30) {
            uploadFrequency = 'Monthly';
          } else {
            uploadFrequency = 'Irregular';
          }
        }
      }

      const stats: YouTubeChannelStats = {
        subscriberCount: channel.subscriberCount,
        videoCount: channel.videoCount,
        viewCount: channel.viewCount,
        lastVideoDate,
        uploadFrequency
      };

      // Cache the stats
      await setCache(cacheKey, stats, CACHE_TTL.YOUTUBE_CHANNEL);

      return stats;

    } catch (error) {
      logger.error('YouTube channel stats error:', error);
      throw createError('Failed to get channel statistics', 500, ErrorCode.YOUTUBE_API_ERROR);
    }
  }

  /**
   * Check and update API quota usage
   */
  private async checkAndUpdateQuota(quotaCost: number) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      // Get or create today's quota record
      let quotaRecord = await prisma.youTubeApiQuota.findUnique({
        where: { date: today }
      });

      if (!quotaRecord) {
        quotaRecord = await prisma.youTubeApiQuota.create({
          data: {
            date: today,
            quotaUsed: 0,
            quotaLimit: YOUTUBE_API.DAILY_QUOTA_LIMIT
          }
        });
      }

      // Check if adding this request would exceed quota
      if (quotaRecord.quotaUsed + quotaCost > quotaRecord.quotaLimit) {
        logger.warn(`YouTube API quota would be exceeded. Used: ${quotaRecord.quotaUsed}, Cost: ${quotaCost}, Limit: ${quotaRecord.quotaLimit}`);
        throw createError('YouTube API daily quota exceeded', 429, ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED);
      }

      // Update quota usage
      await prisma.youTubeApiQuota.update({
        where: { id: quotaRecord.id },
        data: {
          quotaUsed: quotaRecord.quotaUsed + quotaCost,
          searchCalls: quotaCost === YOUTUBE_API.QUOTA_COSTS.SEARCH ? 
            quotaRecord.searchCalls + 1 : quotaRecord.searchCalls,
          channelCalls: quotaCost === YOUTUBE_API.QUOTA_COSTS.CHANNELS ? 
            quotaRecord.channelCalls + 1 : quotaRecord.channelCalls
        }
      });

      logger.debug(`YouTube API quota updated: ${quotaRecord.quotaUsed + quotaCost}/${quotaRecord.quotaLimit}`);

    } catch (error: any) {
      if (error.code === ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED) {
        throw error;
      }
      logger.error('Error updating YouTube API quota:', error);
      // Don't throw here to avoid blocking API calls due to quota tracking issues
    }
  }

  /**
   * Get current quota usage for today
   */
  async getQuotaUsage() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const quotaRecord = await prisma.youTubeApiQuota.findUnique({
      where: { date: today }
    });

    return {
      used: quotaRecord?.quotaUsed || 0,
      limit: quotaRecord?.quotaLimit || YOUTUBE_API.DAILY_QUOTA_LIMIT,
      remaining: (quotaRecord?.quotaLimit || YOUTUBE_API.DAILY_QUOTA_LIMIT) - (quotaRecord?.quotaUsed || 0),
      searchCalls: quotaRecord?.searchCalls || 0,
      channelCalls: quotaRecord?.channelCalls || 0
    };
  }
}

export const youtubeService = new YouTubeService();
