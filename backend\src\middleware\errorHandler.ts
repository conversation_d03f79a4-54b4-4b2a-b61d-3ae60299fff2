import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { ErrorCode } from '@/types/shared';

export interface AppError extends Error {
  statusCode?: number;
  code?: ErrorCode;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  public statusCode: number;
  public code: ErrorCode;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, code: ErrorCode = ErrorCode.DATABASE_ERROR) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  _next: NextFunction
) => {
  let { statusCode = 500, message, code } = error;

  // Log error
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    statusCode,
    code,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    code = ErrorCode.VALIDATION_ERROR;
    message = 'Validation failed';
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401;
    code = ErrorCode.UNAUTHORIZED;
    message = 'Unauthorized access';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    code = ErrorCode.VALIDATION_ERROR;
    message = 'Invalid ID format';
  }

  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Internal server error';
  }

  res.status(statusCode).json({
    success: false,
    error: message,
    code,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
};

export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

export const createError = (message: string, statusCode: number = 500, code: ErrorCode = ErrorCode.DATABASE_ERROR) => {
  return new CustomError(message, statusCode, code);
};
