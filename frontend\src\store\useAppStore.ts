import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { ChannelList, YouTubeChannel, DashboardState, ViewMode } from '@youtube-manager/shared'

interface AppState {
  // UI State
  dashboardState: DashboardState
  sidebarCollapsed: boolean
  
  // Data State
  channelLists: ChannelList[]
  savedChannels: YouTubeChannel[]
  
  // Loading States
  isLoading: boolean
  searchLoading: boolean
  
  // Actions
  setDashboardState: (state: Partial<DashboardState>) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setChannelLists: (lists: ChannelList[]) => void
  addChannelList: (list: ChannelList) => void
  updateChannelList: (listId: string, updates: Partial<ChannelList>) => void
  removeChannelList: (listId: string) => void
  setSavedChannels: (channels: YouTubeChannel[]) => void
  addSavedChannel: (channel: YouTubeChannel) => void
  removeSavedChannel: (channelId: string) => void
  setLoading: (loading: boolean) => void
  setSearchLoading: (loading: boolean) => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial State
      dashboardState: {
        viewMode: { type: 'grid', columns: 3 },
        searchQuery: '',
        filters: {},
        sortBy: 'updatedAt',
        sortOrder: 'desc'
      },
      sidebarCollapsed: false,
      channelLists: [],
      savedChannels: [],
      isLoading: false,
      searchLoading: false,

      // Actions
      setDashboardState: (newState) =>
        set((state) => ({
          dashboardState: { ...state.dashboardState, ...newState }
        })),

      setSidebarCollapsed: (collapsed) =>
        set({ sidebarCollapsed: collapsed }),

      setChannelLists: (lists) =>
        set({ channelLists: lists }),

      addChannelList: (list) =>
        set((state) => ({
          channelLists: [...state.channelLists, list]
        })),

      updateChannelList: (listId, updates) =>
        set((state) => ({
          channelLists: state.channelLists.map((list) =>
            list.id === listId ? { ...list, ...updates } : list
          )
        })),

      removeChannelList: (listId) =>
        set((state) => ({
          channelLists: state.channelLists.filter((list) => list.id !== listId)
        })),

      setSavedChannels: (channels) =>
        set({ savedChannels: channels }),

      addSavedChannel: (channel) =>
        set((state) => ({
          savedChannels: [...state.savedChannels, channel]
        })),

      removeSavedChannel: (channelId) =>
        set((state) => ({
          savedChannels: state.savedChannels.filter((channel) => channel.id !== channelId)
        })),

      setLoading: (loading) =>
        set({ isLoading: loading }),

      setSearchLoading: (loading) =>
        set({ searchLoading: loading })
    }),
    {
      name: 'youtube-manager-store',
      partialize: (state) => ({
        dashboardState: state.dashboardState,
        sidebarCollapsed: state.sidebarCollapsed
      })
    }
  )
)
