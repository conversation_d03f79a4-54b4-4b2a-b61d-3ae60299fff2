# Database
DATABASE_URL="postgresql://username:password@localhost:5432/youtube_manager"

# Redis (optional, for caching)
REDIS_URL="redis://localhost:6379"

# YouTube Data API v3
YOUTUBE_API_KEY="your_youtube_api_key_here"

# JWT Secret
JWT_SECRET="your_super_secret_jwt_key_here"

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL="http://localhost:3000"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Background Jobs
ENABLE_BACKGROUND_JOBS=true
CHANNEL_UPDATE_INTERVAL="0 */6 * * *"
