"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.activityService = exports.ActivityService = void 0;
const database_1 = require("@/utils/database");
const logger_1 = require("@/utils/logger");
const shared_1 = require("@/types/shared");
class ActivityService {
    /**
     * Log a user activity
     */
    async logActivity(userId, type, description, metadata) {
        try {
            const activity = await database_1.prisma.activityLog.create({
                data: {
                    userId,
                    type,
                    description,
                    metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
                },
            });
            logger_1.logger.info('Activity logged', {
                userId,
                type,
                description,
                activityId: activity.id,
            });
            return activity;
        }
        catch (error) {
            logger_1.logger.error('Failed to log activity', {
                userId,
                type,
                description,
                error,
            });
            // Don't throw error to avoid breaking main functionality
        }
    }
    /**
     * Log channel added to list activity
     */
    async logChannelAdded(userId, channelTitle, listName, metadata) {
        return this.logActivity(userId, shared_1.ActivityType.CHANNEL_ADDED, `Added "${channelTitle}" to "${listName}"`, {
            channelTitle,
            listName,
            ...metadata,
        });
    }
    /**
     * Log channel removed from list activity
     */
    async logChannelRemoved(userId, channelTitle, listName, metadata) {
        return this.logActivity(userId, shared_1.ActivityType.CHANNEL_REMOVED, `Removed "${channelTitle}" from "${listName}"`, {
            channelTitle,
            listName,
            ...metadata,
        });
    }
    /**
     * Log list created activity
     */
    async logListCreated(userId, listName, metadata) {
        return this.logActivity(userId, shared_1.ActivityType.LIST_CREATED, `Created list "${listName}"`, {
            listName,
            ...metadata,
        });
    }
    /**
     * Log list updated activity
     */
    async logListUpdated(userId, listName, changes, metadata) {
        const changeDescription = changes.join(', ');
        return this.logActivity(userId, shared_1.ActivityType.LIST_UPDATED, `Updated "${listName}": ${changeDescription}`, {
            listName,
            changes,
            ...metadata,
        });
    }
    /**
     * Log list renamed activity
     */
    async logListRenamed(userId, oldName, newName, metadata) {
        return this.logActivity(userId, shared_1.ActivityType.LIST_RENAMED, `Renamed list from "${oldName}" to "${newName}"`, {
            oldValue: oldName,
            newValue: newName,
            listName: newName,
            ...metadata,
        });
    }
    /**
     * Log list deleted activity
     */
    async logListDeleted(userId, listName, channelCount, metadata) {
        return this.logActivity(userId, shared_1.ActivityType.LIST_DELETED, `Deleted list "${listName}" (${channelCount} channels)`, {
            listName,
            channelCount,
            ...metadata,
        });
    }
    /**
     * Log channel moved between lists activity
     */
    async logChannelMoved(userId, channelTitle, fromList, toList, metadata) {
        return this.logActivity(userId, shared_1.ActivityType.CHANNEL_MOVED, `Moved "${channelTitle}" from "${fromList}" to "${toList}"`, {
            channelTitle,
            oldValue: fromList,
            newValue: toList,
            ...metadata,
        });
    }
    /**
     * Get user activities with pagination
     */
    async getUserActivities(userId, page = 1, limit = 20, type) {
        const skip = (page - 1) * limit;
        const where = {
            userId,
            ...(type && { type }),
        };
        const [activities, total] = await Promise.all([
            database_1.prisma.activityLog.findMany({
                where,
                orderBy: { createdAt: 'desc' },
                skip,
                take: limit,
            }),
            database_1.prisma.activityLog.count({ where }),
        ]);
        return {
            data: activities,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    /**
     * Get recent activities for dashboard
     */
    async getRecentActivities(userId, limit = 10) {
        return database_1.prisma.activityLog.findMany({
            where: { userId },
            orderBy: { createdAt: 'desc' },
            take: limit,
        });
    }
    /**
     * Clean up old activities (older than specified days)
     */
    async cleanupOldActivities(daysToKeep = 90) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        try {
            const result = await database_1.prisma.activityLog.deleteMany({
                where: {
                    createdAt: {
                        lt: cutoffDate,
                    },
                },
            });
            logger_1.logger.info(`Cleaned up ${result.count} old activity logs`);
            return result.count;
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup old activities', error);
            throw error;
        }
    }
    /**
     * Get activity statistics
     */
    async getActivityStats(userId, days = 30) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const activities = await database_1.prisma.activityLog.findMany({
            where: {
                userId,
                createdAt: {
                    gte: startDate,
                },
            },
            select: {
                type: true,
                createdAt: true,
            },
        });
        // Group by type
        const byType = activities.reduce((acc, activity) => {
            acc[activity.type] = (acc[activity.type] || 0) + 1;
            return acc;
        }, {});
        // Group by day
        const byDay = activities.reduce((acc, activity) => {
            const day = activity.createdAt.toISOString().split('T')[0];
            acc[day] = (acc[day] || 0) + 1;
            return acc;
        }, {});
        return {
            total: activities.length,
            byType,
            byDay,
            period: `${days} days`,
        };
    }
}
exports.ActivityService = ActivityService;
exports.activityService = new ActivityService();
//# sourceMappingURL=activityService.js.map