import { YouTubeSearchFilters } from '@youtube-manager/shared'
import { ChannelCard } from '@/components/Channel/ChannelCard'
import { useYouTubeSearch } from '@/hooks/useYouTubeSearch'

interface SearchResultsProps {
  query: string
  filters: YouTubeSearchFilters
  isSearching: boolean
}

// Mock search results - will be replaced with real API data
const mockResults = [
  {
    id: '1',
    channelId: 'UC_x5XG1OV2P6uZZ5FSM9Ttw',
    title: 'Google for Developers',
    description: 'Subscribe to Google Developers →  https://goo.gle/developers',
    thumbnailUrl: 'https://via.placeholder.com/120',
    subscriberCount: 2340000,
    videoCount: 4521,
    viewCount: 234567890,
    publishedAt: new Date('2007-05-23'),
    customUrl: '@GoogleDevelopers',
    country: 'US',
    lastUpdated: new Date()
  },
  {
    id: '2',
    channelId: 'UCWv7vMbMWH4-V0ZXdmDpPBA',
    title: 'Programming with <PERSON><PERSON>',
    description: 'I train professional software engineers that companies love to hire.',
    thumbnailUrl: 'https://via.placeholder.com/120',
    subscriberCount: 3200000,
    videoCount: 234,
    viewCount: 156789012,
    publishedAt: new Date('2014-02-11'),
    customUrl: '@programmingwithmosh',
    country: 'CA',
    lastUpdated: new Date()
  },
  {
    id: '3',
    channelId: 'UC8butISFwT-Wl7EV0hUK0BQ',
    title: 'freeCodeCamp.org',
    description: 'Learn to code for free.',
    thumbnailUrl: 'https://via.placeholder.com/120',
    subscriberCount: 8900000,
    videoCount: 1876,
    viewCount: 567890123,
    publishedAt: new Date('2014-12-17'),
    customUrl: '@freecodecamp',
    country: 'US',
    lastUpdated: new Date()
  }
]

export function SearchResults({ query, filters, isSearching }: SearchResultsProps) {
  if (!query && !isSearching) {
    return (
      <div className="card text-center py-12">
        <div className="text-slate-400 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-lg font-medium text-slate-300 mb-2">Start Your Search</h3>
          <p>Enter keywords to discover YouTube channels and content creators</p>
        </div>
      </div>
    )
  }

  if (isSearching) {
    return (
      <div className="card">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-slate-400">Searching YouTube channels...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-white">Search Results</h2>
          <p className="text-slate-400 text-sm">
            Found {mockResults.length} channels for "{query}"
          </p>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-slate-400">
          <span>Sort by:</span>
          <select className="bg-dark-700 border border-dark-600 rounded px-2 py-1 text-white text-sm">
            <option>Relevance</option>
            <option>Subscriber Count</option>
            <option>View Count</option>
            <option>Upload Date</option>
          </select>
        </div>
      </div>

      {/* Results Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockResults.map((channel) => (
          <ChannelCard
            key={channel.id}
            channel={channel}
            showAddButton={true}
          />
        ))}
      </div>

      {/* Load More */}
      <div className="text-center">
        <button className="btn-secondary btn-md">
          Load More Results
        </button>
      </div>
    </div>
  )
}
