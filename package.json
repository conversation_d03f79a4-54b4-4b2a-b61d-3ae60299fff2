{"name": "youtube-content-manager", "version": "1.0.0", "description": "A modern web application for organizing, tracking, and analyzing YouTube channels and content", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:shared": "cd shared && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../shared && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.3", "@types/node-cron": "^3.0.11", "@types/socket.io": "^3.0.1", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "concurrently": "^8.2.2", "eslint": "^9.29.0", "nodemon": "^3.1.10", "prisma": "^6.10.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["youtube", "content-management", "react", "nodejs", "typescript", "youtube-api"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^6.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "googleapis": "^150.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^4.1.0", "redis": "^5.5.6", "socket.io": "^4.8.1", "winston": "^3.17.0", "zod": "^3.25.67"}}