{"name": "youtube-content-manager", "version": "1.0.0", "description": "A modern web application for organizing, tracking, and analyzing YouTube channels and content", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:shared": "cd shared && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../shared && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["youtube", "content-management", "react", "nodejs", "typescript", "youtube-api"], "author": "Your Name", "license": "MIT"}