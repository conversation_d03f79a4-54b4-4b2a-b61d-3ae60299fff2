"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createError = exports.asyncHandler = exports.errorHandler = exports.CustomError = void 0;
const logger_1 = require("@/utils/logger");
const shared_1 = require("@/types/shared");
class CustomError extends Error {
    constructor(message, statusCode = 500, code = shared_1.ErrorCode.DATABASE_ERROR) {
        super(message);
        this.statusCode = statusCode;
        this.code = code;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
const errorHandler = (error, req, res, _next) => {
    let { statusCode = 500, message, code } = error;
    // Log error
    logger_1.logger.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        statusCode,
        code,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    // Handle specific error types
    if (error.name === 'ValidationError') {
        statusCode = 400;
        code = shared_1.ErrorCode.VALIDATION_ERROR;
        message = 'Validation failed';
    }
    else if (error.name === 'UnauthorizedError') {
        statusCode = 401;
        code = shared_1.ErrorCode.UNAUTHORIZED;
        message = 'Unauthorized access';
    }
    else if (error.name === 'CastError') {
        statusCode = 400;
        code = shared_1.ErrorCode.VALIDATION_ERROR;
        message = 'Invalid ID format';
    }
    // Don't leak error details in production
    if (process.env.NODE_ENV === 'production' && statusCode === 500) {
        message = 'Internal server error';
    }
    res.status(statusCode).json({
        success: false,
        error: message,
        code,
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};
exports.asyncHandler = asyncHandler;
const createError = (message, statusCode = 500, code = shared_1.ErrorCode.DATABASE_ERROR) => {
    return new CustomError(message, statusCode, code);
};
exports.createError = createError;
//# sourceMappingURL=errorHandler.js.map