import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { logger } from '@/utils/logger';
import { prisma } from '@/utils/database';
import { youtubeService } from '@/services/youtubeService';
import { cacheService } from '@/services/cacheService';

export class RealtimeService {
  private io: SocketIOServer | null = null;
  private updateQueue = new Map<string, NodeJS.Timeout>();

  /**
   * Initialize Socket.IO server
   */
  initialize(httpServer: HTTPServer): void {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:3000',
        methods: ['GET', 'POST']
      }
    });

    this.setupEventHandlers();
    logger.info('Real-time service initialized');
  }

  /**
   * Set up Socket.IO event handlers
   */
  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      logger.debug(`Client connected: ${socket.id}`);

      // Join user-specific room for targeted updates
      socket.on('join-user-room', (userId: string) => {
        socket.join(`user:${userId}`);
        logger.debug(`Client ${socket.id} joined room: user:${userId}`);
      });

      // Handle channel subscription for real-time updates
      socket.on('subscribe-channel', (channelId: string) => {
        socket.join(`channel:${channelId}`);
        this.scheduleChannelUpdate(channelId);
        logger.debug(`Client ${socket.id} subscribed to channel: ${channelId}`);
      });

      socket.on('unsubscribe-channel', (channelId: string) => {
        socket.leave(`channel:${channelId}`);
        logger.debug(`Client ${socket.id} unsubscribed from channel: ${channelId}`);
      });

      socket.on('disconnect', () => {
        logger.debug(`Client disconnected: ${socket.id}`);
      });
    });
  }

  /**
   * Broadcast channel list update to user
   */
  broadcastChannelListUpdate(userId: string, listId: string, action: string, data?: any): void {
    if (!this.io) return;

    this.io.to(`user:${userId}`).emit('channel-list-updated', {
      listId,
      action,
      data,
      timestamp: new Date().toISOString()
    });

    logger.debug(`Broadcasted channel list update to user ${userId}: ${action}`);
  }

  /**
   * Broadcast channel statistics update
   */
  broadcastChannelUpdate(channelId: string, stats: any): void {
    if (!this.io) return;

    this.io.to(`channel:${channelId}`).emit('channel-updated', {
      channelId,
      stats,
      timestamp: new Date().toISOString()
    });

    logger.debug(`Broadcasted channel update: ${channelId}`);
  }

  /**
   * Broadcast activity update to user
   */
  broadcastActivityUpdate(userId: string, activity: any): void {
    if (!this.io) return;

    this.io.to(`user:${userId}`).emit('activity-updated', {
      activity,
      timestamp: new Date().toISOString()
    });

    logger.debug(`Broadcasted activity update to user ${userId}`);
  }

  /**
   * Broadcast quota warning
   */
  broadcastQuotaWarning(threshold: number, current: number, limit: number): void {
    if (!this.io) return;

    this.io.emit('quota-warning', {
      threshold,
      current,
      limit,
      percentage: (current / limit) * 100,
      timestamp: new Date().toISOString()
    });

    logger.warn(`Broadcasted quota warning: ${current}/${limit} (${threshold}% threshold)`);
  }

  /**
   * Schedule a channel update with debouncing
   */
  private scheduleChannelUpdate(channelId: string): void {
    // Clear existing timeout for this channel
    const existingTimeout = this.updateQueue.get(channelId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Schedule new update with 30-second debounce
    const timeout = setTimeout(async () => {
      await this.updateChannelStats(channelId);
      this.updateQueue.delete(channelId);
    }, 30000);

    this.updateQueue.set(channelId, timeout);
  }

  /**
   * Update channel statistics and broadcast changes
   */
  private async updateChannelStats(channelId: string): Promise<void> {
    try {
      // Get current channel data from database
      const channel = await prisma.youTubeChannel.findUnique({
        where: { id: channelId }
      });

      if (!channel) {
        logger.warn(`Channel not found for update: ${channelId}`);
        return;
      }

      // Fetch latest stats from YouTube API
      const updatedChannel = await youtubeService.getChannelById(channel.channelId);
      if (!updatedChannel) {
        logger.warn(`Failed to fetch updated stats for channel: ${channel.channelId}`);
        return;
      }

      // Check if there are significant changes
      const hasSignificantChanges = this.hasSignificantChanges(channel, updatedChannel);

      if (hasSignificantChanges) {
        // Update database
        await prisma.youTubeChannel.update({
          where: { id: channelId },
          data: {
            subscriberCount: updatedChannel.subscriberCount,
            videoCount: updatedChannel.videoCount,
            viewCount: updatedChannel.viewCount,
            lastUpdated: new Date()
          }
        });

        // Invalidate cache
        await cacheService.delete(`youtube:channel:${channel.channelId}`);

        // Broadcast update
        this.broadcastChannelUpdate(channelId, {
          subscriberCount: updatedChannel.subscriberCount,
          videoCount: updatedChannel.videoCount,
          viewCount: updatedChannel.viewCount,
          changes: this.getChanges(channel, updatedChannel)
        });

        logger.info(`Updated channel stats: ${channel.title}`);
      }

    } catch (error) {
      logger.error(`Failed to update channel stats for ${channelId}:`, error);
    }
  }

  /**
   * Check if channel changes are significant enough to broadcast
   */
  private hasSignificantChanges(oldChannel: any, newChannel: any): boolean {
    const subscriberDiff = Math.abs(newChannel.subscriberCount - oldChannel.subscriberCount);
    const videoDiff = newChannel.videoCount - oldChannel.videoCount;
    const viewDiff = Number(newChannel.viewCount) - Number(oldChannel.viewCount);

    // Consider changes significant if:
    // - Subscriber count changed by more than 100 or 1%
    // - Video count increased
    // - View count increased by more than 10,000 or 1%
    return (
      subscriberDiff > 100 || 
      subscriberDiff > oldChannel.subscriberCount * 0.01 ||
      videoDiff > 0 ||
      viewDiff > 10000 ||
      viewDiff > Number(oldChannel.viewCount) * 0.01
    );
  }

  /**
   * Get specific changes between old and new channel data
   */
  private getChanges(oldChannel: any, newChannel: any): any {
    const changes: any = {};

    if (newChannel.subscriberCount !== oldChannel.subscriberCount) {
      changes.subscribers = {
        old: oldChannel.subscriberCount,
        new: newChannel.subscriberCount,
        diff: newChannel.subscriberCount - oldChannel.subscriberCount
      };
    }

    if (newChannel.videoCount !== oldChannel.videoCount) {
      changes.videos = {
        old: oldChannel.videoCount,
        new: newChannel.videoCount,
        diff: newChannel.videoCount - oldChannel.videoCount
      };
    }

    if (Number(newChannel.viewCount) !== Number(oldChannel.viewCount)) {
      changes.views = {
        old: Number(oldChannel.viewCount),
        new: Number(newChannel.viewCount),
        diff: Number(newChannel.viewCount) - Number(oldChannel.viewCount)
      };
    }

    return changes;
  }

  /**
   * Get connected clients count
   */
  getConnectedClientsCount(): number {
    return this.io ? this.io.engine.clientsCount : 0;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    // Clear all pending updates
    for (const timeout of this.updateQueue.values()) {
      clearTimeout(timeout);
    }
    this.updateQueue.clear();

    if (this.io) {
      this.io.close();
      this.io = null;
    }

    logger.info('Real-time service cleaned up');
  }
}

export const realtimeService = new RealtimeService();
