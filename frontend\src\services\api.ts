import axios from 'axios'
import { 
  ApiResponse, 
  PaginatedResponse, 
  YouTubeChannel, 
  ChannelList, 
  ActivityLog, 
  YouTubeSearchFilters,
  YouTubeChannelStats
} from '@youtube-manager/shared'

// Create axios instance with default config
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// YouTube API endpoints
export const youtubeApi = {
  searchChannels: async (filters: YouTubeSearchFilters, maxResults?: number): Promise<{
    channels: YouTubeChannel[]
    totalResults: number
    nextPageToken?: string
  }> => {
    const response = await api.post('/youtube/search', filters, {
      params: { maxResults }
    })
    return response.data.data
  },

  getChannel: async (channelId: string): Promise<YouTubeChannel> => {
    const response = await api.get(`/youtube/channel/${channelId}`)
    return response.data.data
  },

  getChannelStats: async (channelId: string): Promise<YouTubeChannelStats> => {
    const response = await api.get(`/youtube/channel/${channelId}/stats`)
    return response.data.data
  },

  getChannels: async (channelIds: string[]): Promise<YouTubeChannel[]> => {
    const response = await api.post('/youtube/channels', { channelIds })
    return response.data.data
  },

  getQuotaUsage: async (): Promise<{
    used: number
    limit: number
    remaining: number
    searchCalls: number
    channelCalls: number
  }> => {
    const response = await api.get('/youtube/quota')
    return response.data.data
  }
}

// Channel Lists API endpoints
export const channelListsApi = {
  getAll: async (): Promise<ChannelList[]> => {
    const response = await api.get('/channel-lists')
    return response.data.data
  },

  getById: async (listId: string): Promise<ChannelList> => {
    const response = await api.get(`/channel-lists/${listId}`)
    return response.data.data
  },

  create: async (data: {
    name: string
    description?: string
    color: string
    isPublic?: boolean
  }): Promise<ChannelList> => {
    const response = await api.post('/channel-lists', data)
    return response.data.data
  },

  update: async (listId: string, data: {
    name?: string
    description?: string
    color?: string
    isPublic?: boolean
  }): Promise<ChannelList> => {
    const response = await api.put(`/channel-lists/${listId}`, data)
    return response.data.data
  },

  delete: async (listId: string): Promise<void> => {
    await api.delete(`/channel-lists/${listId}`)
  },

  addChannel: async (listId: string, channelId: string): Promise<void> => {
    await api.post(`/channel-lists/${listId}/channels`, { channelId })
  },

  removeChannel: async (listId: string, channelId: string): Promise<void> => {
    await api.delete(`/channel-lists/${listId}/channels/${channelId}`)
  }
}

// Channels API endpoints
export const channelsApi = {
  getAll: async (): Promise<YouTubeChannel[]> => {
    const response = await api.get('/channels')
    return response.data.data
  },

  save: async (channelData: Partial<YouTubeChannel>): Promise<YouTubeChannel> => {
    const response = await api.post('/channels', channelData)
    return response.data.data
  },

  remove: async (channelId: string): Promise<void> => {
    await api.delete(`/channels/${channelId}`)
  }
}

// Activity API endpoints
export const activityApi = {
  getAll: async (page?: number, limit?: number): Promise<PaginatedResponse<ActivityLog>> => {
    const response = await api.get('/activity', {
      params: { page, limit }
    })
    return response.data.data
  }
}

// Auth API endpoints
export const authApi = {
  register: async (data: {
    email: string
    name: string
    password: string
  }): Promise<{ user: any; token: string }> => {
    const response = await api.post('/auth/register', data)
    return response.data.data
  },

  login: async (data: {
    email: string
    password: string
  }): Promise<{ user: any; token: string }> => {
    const response = await api.post('/auth/login', data)
    return response.data.data
  },

  logout: async (): Promise<void> => {
    await api.post('/auth/logout')
  },

  getProfile: async (): Promise<any> => {
    const response = await api.get('/auth/me')
    return response.data.data
  }
}

export default api
