"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheService = exports.CacheService = void 0;
const redis_1 = require("@/utils/redis");
const logger_1 = require("@/utils/logger");
const shared_1 = require("@/types/shared");
class CacheService {
    constructor() {
        this.memoryCache = new Map();
        this.DEFAULT_TTL = 300; // 5 minutes
    }
    /**
     * Get data from cache (Redis first, then memory fallback)
     */
    async get(key) {
        try {
            // Try Redis first
            const redisData = await (0, redis_1.getCache)(key);
            if (redisData !== null) {
                logger_1.logger.debug(`Cache hit (Redis): ${key}`);
                return redisData;
            }
            // Fallback to memory cache
            const memoryData = this.memoryCache.get(key);
            if (memoryData && memoryData.expires > Date.now()) {
                logger_1.logger.debug(`Cache hit (Memory): ${key}`);
                return memoryData.data;
            }
            // Clean up expired memory cache entry
            if (memoryData) {
                this.memoryCache.delete(key);
            }
            logger_1.logger.debug(`Cache miss: ${key}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Cache get error for key ${key}:`, error);
            return null;
        }
    }
    /**
     * Set data in cache (both Redis and memory)
     */
    async set(key, data, ttlSeconds) {
        const ttl = ttlSeconds || this.DEFAULT_TTL;
        try {
            // Set in Redis
            await (0, redis_1.setCache)(key, data, ttl);
            // Set in memory cache as backup
            this.memoryCache.set(key, {
                data,
                expires: Date.now() + (ttl * 1000)
            });
            logger_1.logger.debug(`Cache set: ${key} (TTL: ${ttl}s)`);
        }
        catch (error) {
            logger_1.logger.error(`Cache set error for key ${key}:`, error);
        }
    }
    /**
     * Delete specific cache key
     */
    async delete(key) {
        try {
            await (0, redis_1.deleteCache)(key);
            this.memoryCache.delete(key);
            logger_1.logger.debug(`Cache deleted: ${key}`);
        }
        catch (error) {
            logger_1.logger.error(`Cache delete error for key ${key}:`, error);
        }
    }
    /**
     * Delete cache keys matching pattern
     */
    async deletePattern(pattern) {
        try {
            await (0, redis_1.deleteCachePattern)(pattern);
            // Clean memory cache with pattern matching
            for (const key of this.memoryCache.keys()) {
                if (this.matchesPattern(key, pattern)) {
                    this.memoryCache.delete(key);
                }
            }
            logger_1.logger.debug(`Cache pattern deleted: ${pattern}`);
        }
        catch (error) {
            logger_1.logger.error(`Cache delete pattern error for ${pattern}:`, error);
        }
    }
    /**
     * Get or set cache with a function
     */
    async getOrSet(key, fetchFunction, ttlSeconds) {
        const cached = await this.get(key);
        if (cached !== null) {
            return cached;
        }
        const data = await fetchFunction();
        await this.set(key, data, ttlSeconds);
        return data;
    }
    /**
     * Invalidate all YouTube-related caches
     */
    async invalidateYouTubeCache() {
        try {
            await Promise.all([
                this.deletePattern(`${shared_1.CACHE_KEYS.YOUTUBE_CHANNEL}*`),
                this.deletePattern(`${shared_1.CACHE_KEYS.YOUTUBE_SEARCH}*`)
            ]);
            logger_1.logger.info('YouTube cache invalidated');
        }
        catch (error) {
            logger_1.logger.error('Failed to invalidate YouTube cache:', error);
        }
    }
    /**
     * Invalidate user-specific caches
     */
    async invalidateUserCache(userId) {
        try {
            await Promise.all([
                this.deletePattern(`${shared_1.CACHE_KEYS.CHANNEL_LISTS}${userId}*`),
                this.deletePattern(`${shared_1.CACHE_KEYS.USER_ACTIVITY}${userId}*`)
            ]);
            logger_1.logger.info(`User cache invalidated for user: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to invalidate user cache for ${userId}:`, error);
        }
    }
    /**
     * Clean up expired memory cache entries
     */
    cleanupMemoryCache() {
        const now = Date.now();
        let cleanedCount = 0;
        for (const [key, value] of this.memoryCache.entries()) {
            if (value.expires <= now) {
                this.memoryCache.delete(key);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            logger_1.logger.debug(`Cleaned up ${cleanedCount} expired memory cache entries`);
        }
    }
    /**
     * Get cache statistics
     */
    async getCacheStats() {
        const redisClient = (0, redis_1.getRedisClient)();
        return {
            memorySize: this.memoryCache.size,
            redisConnected: redisClient !== null,
            memoryKeys: Array.from(this.memoryCache.keys())
        };
    }
    /**
     * Warm up cache with frequently accessed data
     */
    async warmupCache() {
        try {
            logger_1.logger.info('Starting cache warmup...');
            // This could be expanded to pre-load frequently accessed data
            // For now, we'll just log that warmup is available
            logger_1.logger.info('Cache warmup completed');
        }
        catch (error) {
            logger_1.logger.error('Cache warmup failed:', error);
        }
    }
    /**
     * Simple pattern matching for cache keys
     */
    matchesPattern(key, pattern) {
        // Convert glob pattern to regex
        const regexPattern = pattern
            .replace(/\*/g, '.*')
            .replace(/\?/g, '.');
        return new RegExp(`^${regexPattern}$`).test(key);
    }
}
exports.CacheService = CacheService;
// Create singleton instance
exports.cacheService = new CacheService();
// Set up periodic memory cache cleanup
setInterval(() => {
    exports.cacheService.cleanupMemoryCache();
}, 5 * 60 * 1000); // Every 5 minutes
//# sourceMappingURL=cacheService.js.map