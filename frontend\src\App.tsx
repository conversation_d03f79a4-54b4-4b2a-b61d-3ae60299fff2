import { Routes, Route } from 'react-router-dom'
import { Layout } from '@/components/Layout'
import { Dashboard } from '@/pages/Dashboard'
import { Search } from '@/pages/Search'
import { ChannelLists } from '@/pages/ChannelLists'
import { Settings } from '@/pages/Settings'
import { useRealtime } from '@/hooks/useRealtime'

function App() {
  // Initialize real-time connection
  // TODO: Replace with actual user ID from auth
  const { isConnected, connectionError } = useRealtime({
    userId: 'temp-user-id',
    autoConnect: true
  })

  return (
    <Layout>
      {/* Connection status indicator */}
      {connectionError && (
        <div className="bg-red-900/20 border border-red-800 text-red-400 px-4 py-2 text-sm">
          Real-time connection error: {connectionError}
        </div>
      )}

      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/search" element={<Search />} />
        <Route path="/channel-lists" element={<ChannelLists />} />
        <Route path="/channel-lists/:listId" element={<ChannelLists />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Layout>
  )
}

export default App
