import { useState } from 'react'
import { Users, Video, Eye, Plus, Check, ExternalLink } from 'lucide-react'
import { YouTubeChannel } from '@youtube-manager/shared'
import { formatSubscriberCount, formatViewCount, formatTimeAgo } from '@youtube-manager/shared'

interface ChannelCardProps {
  channel: YouTubeChannel
  showAddButton?: boolean
  isAdded?: boolean
  onAdd?: (channel: YouTubeChannel) => void
}

export function ChannelCard({ channel, showAddButton = false, isAdded = false, onAdd }: ChannelCardProps) {
  const [isAdding, setIsAdding] = useState(false)

  const handleAdd = async () => {
    if (isAdding || isAdded) return
    
    setIsAdding(true)
    try {
      await onAdd?.(channel)
    } finally {
      setIsAdding(false)
    }
  }

  const openChannel = () => {
    const url = channel.customUrl 
      ? `https://youtube.com/${channel.customUrl}`
      : `https://youtube.com/channel/${channel.channelId}`
    window.open(url, '_blank')
  }

  return (
    <div className="card hover:border-dark-600 transition-all group">
      {/* Channel Header */}
      <div className="flex items-start gap-4 mb-4">
        <img
          src={channel.thumbnailUrl}
          alt={channel.title}
          className="w-16 h-16 rounded-full flex-shrink-0"
        />
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-white truncate group-hover:text-primary-400 transition-colors">
            {channel.title}
          </h3>
          {channel.customUrl && (
            <p className="text-sm text-slate-400 truncate">
              {channel.customUrl}
            </p>
          )}
          <div className="flex items-center gap-2 mt-1">
            {channel.country && (
              <span className="text-xs bg-dark-700 text-slate-300 px-2 py-1 rounded">
                {channel.country}
              </span>
            )}
            <span className="text-xs text-slate-500">
              Joined {formatTimeAgo(channel.publishedAt)}
            </span>
          </div>
        </div>
      </div>

      {/* Channel Description */}
      {channel.description && (
        <p className="text-sm text-slate-400 mb-4 line-clamp-2">
          {channel.description}
        </p>
      )}

      {/* Channel Stats */}
      <div className="grid grid-cols-3 gap-4 mb-4 text-center">
        <div>
          <div className="flex items-center justify-center gap-1 text-slate-400 mb-1">
            <Users className="w-4 h-4" />
          </div>
          <div className="text-sm font-medium text-white">
            {formatSubscriberCount(channel.subscriberCount)}
          </div>
          <div className="text-xs text-slate-500">Subscribers</div>
        </div>
        <div>
          <div className="flex items-center justify-center gap-1 text-slate-400 mb-1">
            <Video className="w-4 h-4" />
          </div>
          <div className="text-sm font-medium text-white">
            {channel.videoCount.toLocaleString()}
          </div>
          <div className="text-xs text-slate-500">Videos</div>
        </div>
        <div>
          <div className="flex items-center justify-center gap-1 text-slate-400 mb-1">
            <Eye className="w-4 h-4" />
          </div>
          <div className="text-sm font-medium text-white">
            {formatViewCount(Number(channel.viewCount))}
          </div>
          <div className="text-xs text-slate-500">Views</div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <button
          onClick={openChannel}
          className="btn-ghost btn-sm flex-1"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Visit
        </button>
        
        {showAddButton && (
          <button
            onClick={handleAdd}
            disabled={isAdding || isAdded}
            className={`btn-sm flex-1 ${
              isAdded 
                ? 'bg-success-600 text-white cursor-default' 
                : 'btn-primary'
            }`}
          >
            {isAdding ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : isAdded ? (
              <Check className="w-4 h-4 mr-2" />
            ) : (
              <Plus className="w-4 h-4 mr-2" />
            )}
            {isAdded ? 'Added' : 'Add'}
          </button>
        )}
      </div>
    </div>
  )
}
