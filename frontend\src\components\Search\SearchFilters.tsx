import { YouTubeSearchFilters } from '@youtube-manager/shared'

interface SearchFiltersProps {
  filters: YouTubeSearchFilters
  onFiltersChange: (filters: YouTubeSearchFilters) => void
}

export function SearchFilters({ filters, onFiltersChange }: SearchFiltersProps) {
  const updateFilter = (key: keyof YouTubeSearchFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  return (
    <div className="border-t border-dark-600 pt-4 space-y-4">
      <h3 className="text-sm font-medium text-slate-300">Search Filters</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Subscriber Count Range */}
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Subscriber Count
          </label>
          <div className="space-y-2">
            <input
              type="number"
              placeholder="Min subscribers"
              value={filters.minSubscribers || ''}
              onChange={(e) => updateFilter('minSubscribers', e.target.value ? parseInt(e.target.value) : undefined)}
              className="input w-full text-sm"
            />
            <input
              type="number"
              placeholder="Max subscribers"
              value={filters.maxSubscribers || ''}
              onChange={(e) => updateFilter('maxSubscribers', e.target.value ? parseInt(e.target.value) : undefined)}
              className="input w-full text-sm"
            />
          </div>
        </div>

        {/* View Count Range */}
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            View Count
          </label>
          <div className="space-y-2">
            <input
              type="number"
              placeholder="Min views"
              value={filters.minViews || ''}
              onChange={(e) => updateFilter('minViews', e.target.value ? parseInt(e.target.value) : undefined)}
              className="input w-full text-sm"
            />
            <input
              type="number"
              placeholder="Max views"
              value={filters.maxViews || ''}
              onChange={(e) => updateFilter('maxViews', e.target.value ? parseInt(e.target.value) : undefined)}
              className="input w-full text-sm"
            />
          </div>
        </div>

        {/* Country */}
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Country
          </label>
          <select
            value={filters.country || ''}
            onChange={(e) => updateFilter('country', e.target.value || undefined)}
            className="input w-full text-sm"
          >
            <option value="">Any Country</option>
            <option value="US">United States</option>
            <option value="GB">United Kingdom</option>
            <option value="CA">Canada</option>
            <option value="AU">Australia</option>
            <option value="DE">Germany</option>
            <option value="FR">France</option>
            <option value="JP">Japan</option>
            <option value="KR">South Korea</option>
            <option value="IN">India</option>
            <option value="BR">Brazil</option>
          </select>
        </div>

        {/* Sort Order */}
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Sort By
          </label>
          <select
            value={filters.order || 'relevance'}
            onChange={(e) => updateFilter('order', e.target.value as any)}
            className="input w-full text-sm"
          >
            <option value="relevance">Relevance</option>
            <option value="date">Upload Date</option>
            <option value="rating">Rating</option>
            <option value="viewCount">View Count</option>
            <option value="title">Title</option>
          </select>
        </div>
      </div>

      {/* Date Range */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Published After
          </label>
          <input
            type="date"
            value={filters.publishedAfter ? filters.publishedAfter.toISOString().split('T')[0] : ''}
            onChange={(e) => updateFilter('publishedAfter', e.target.value ? new Date(e.target.value) : undefined)}
            className="input w-full text-sm"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-2">
            Published Before
          </label>
          <input
            type="date"
            value={filters.publishedBefore ? filters.publishedBefore.toISOString().split('T')[0] : ''}
            onChange={(e) => updateFilter('publishedBefore', e.target.value ? new Date(e.target.value) : undefined)}
            className="input w-full text-sm"
          />
        </div>
      </div>

      {/* Clear Filters */}
      <div className="flex justify-end">
        <button
          type="button"
          onClick={() => onFiltersChange({})}
          className="text-sm text-slate-400 hover:text-white"
        >
          Clear all filters
        </button>
      </div>
    </div>
  )
}
