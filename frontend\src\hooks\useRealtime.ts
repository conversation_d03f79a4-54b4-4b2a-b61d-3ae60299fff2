import { useEffect, useRef, useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { io, Socket } from 'socket.io-client'

interface UseRealtimeOptions {
  userId?: string
  autoConnect?: boolean
}

export function useRealtime({ userId, autoConnect = true }: UseRealtimeOptions = {}) {
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const socketRef = useRef<Socket | null>(null)
  const queryClient = useQueryClient()

  useEffect(() => {
    if (!autoConnect) return

    // Initialize socket connection
    const socket = io(process.env.VITE_API_URL || 'http://localhost:3001', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
    })

    socketRef.current = socket

    // Connection event handlers
    socket.on('connect', () => {
      setIsConnected(true)
      setConnectionError(null)
      console.log('Connected to real-time server')

      // Join user room if userId is provided
      if (userId) {
        socket.emit('join-user-room', userId)
      }
    })

    socket.on('disconnect', () => {
      setIsConnected(false)
      console.log('Disconnected from real-time server')
    })

    socket.on('connect_error', (error) => {
      setConnectionError(error.message)
      console.error('Real-time connection error:', error)
    })

    // Channel list update handler
    socket.on('channel-list-updated', (data) => {
      console.log('Channel list updated:', data)
      
      // Invalidate channel lists cache
      queryClient.invalidateQueries({ queryKey: ['channel-lists'] })
      
      // Show toast notification
      toast.success(`Channel list ${data.action}`)
    })

    // Channel update handler
    socket.on('channel-updated', (data) => {
      console.log('Channel updated:', data)
      
      // Invalidate channel cache
      queryClient.invalidateQueries({ queryKey: ['youtube-channel', data.channelId] })
      
      // Show notification for significant changes
      if (data.stats.changes) {
        const changes = data.stats.changes
        let message = 'Channel updated: '
        
        if (changes.subscribers) {
          const diff = changes.subscribers.diff
          message += `${diff > 0 ? '+' : ''}${diff.toLocaleString()} subscribers`
        }
        
        if (changes.videos && changes.videos.diff > 0) {
          message += `${message.includes('subscribers') ? ', ' : ''}+${changes.videos.diff} videos`
        }
        
        toast.success(message)
      }
    })

    // Activity update handler
    socket.on('activity-updated', (data) => {
      console.log('Activity updated:', data)
      
      // Invalidate activity cache
      queryClient.invalidateQueries({ queryKey: ['recent-activity'] })
      queryClient.invalidateQueries({ queryKey: ['activity-log'] })
    })

    // Quota warning handler
    socket.on('quota-warning', (data) => {
      console.warn('Quota warning:', data)
      
      toast.error(
        `YouTube API quota warning: ${data.current}/${data.limit} (${data.percentage.toFixed(1)}%)`,
        { duration: 8000 }
      )
      
      // Invalidate quota cache
      queryClient.invalidateQueries({ queryKey: ['youtube-quota'] })
    })

    // Cleanup on unmount
    return () => {
      socket.disconnect()
      socketRef.current = null
    }
  }, [userId, autoConnect, queryClient])

  // Subscribe to channel updates
  const subscribeToChannel = (channelId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('subscribe-channel', channelId)
    }
  }

  // Unsubscribe from channel updates
  const unsubscribeFromChannel = (channelId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('unsubscribe-channel', channelId)
    }
  }

  // Manual connect/disconnect
  const connect = () => {
    if (socketRef.current && !socketRef.current.connected) {
      socketRef.current.connect()
    }
  }

  const disconnect = () => {
    if (socketRef.current?.connected) {
      socketRef.current.disconnect()
    }
  }

  return {
    isConnected,
    connectionError,
    subscribeToChannel,
    unsubscribeFromChannel,
    connect,
    disconnect,
    socket: socketRef.current
  }
}

// Hook for subscribing to specific channel updates
export function useChannelSubscription(channelId: string, enabled: boolean = true) {
  const { subscribeToChannel, unsubscribeFromChannel, isConnected } = useRealtime()

  useEffect(() => {
    if (enabled && isConnected && channelId) {
      subscribeToChannel(channelId)
      
      return () => {
        unsubscribeFromChannel(channelId)
      }
    }
  }, [channelId, enabled, isConnected, subscribeToChannel, unsubscribeFromChannel])

  return { isConnected }
}

// Hook for quota monitoring
export function useQuotaMonitoring() {
  const [quotaStatus, setQuotaStatus] = useState<{
    current: number
    limit: number
    percentage: number
    warning: boolean
  } | null>(null)

  const { socket } = useRealtime()

  useEffect(() => {
    if (!socket) return

    const handleQuotaWarning = (data: any) => {
      setQuotaStatus({
        current: data.current,
        limit: data.limit,
        percentage: data.percentage,
        warning: data.percentage > 80
      })
    }

    socket.on('quota-warning', handleQuotaWarning)

    return () => {
      socket.off('quota-warning', handleQuotaWarning)
    }
  }, [socket])

  return quotaStatus
}
