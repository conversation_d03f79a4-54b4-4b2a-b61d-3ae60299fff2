import { getRedisClient, setCache, getCache, deleteCache, deleteCachePattern } from '@/utils/redis';
import { logger } from '@/utils/logger';
import { CACHE_KEYS, CACHE_TTL } from '@/types/shared';

export class CacheService {
  private memoryCache = new Map<string, { data: any; expires: number }>();
  private readonly DEFAULT_TTL = 300; // 5 minutes

  /**
   * Get data from cache (Redis first, then memory fallback)
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      // Try Redis first
      const redisData = await getCache<T>(key);
      if (redisData !== null) {
        logger.debug(`Cache hit (Redis): ${key}`);
        return redisData;
      }

      // Fallback to memory cache
      const memoryData = this.memoryCache.get(key);
      if (memoryData && memoryData.expires > Date.now()) {
        logger.debug(`Cache hit (Memory): ${key}`);
        return memoryData.data as T;
      }

      // Clean up expired memory cache entry
      if (memoryData) {
        this.memoryCache.delete(key);
      }

      logger.debug(`Cache miss: ${key}`);
      return null;
    } catch (error: any) {
      logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Set data in cache (both Redis and memory)
   */
  async set(key: string, data: any, ttlSeconds?: number): Promise<void> {
    const ttl = ttlSeconds || this.DEFAULT_TTL;

    try {
      // Set in Redis
      await setCache(key, data, ttl);

      // Set in memory cache as backup
      this.memoryCache.set(key, {
        data,
        expires: Date.now() + (ttl * 1000)
      });

      logger.debug(`Cache set: ${key} (TTL: ${ttl}s)`);
    } catch (error: any) {
      logger.error(`Cache set error for key ${key}:`, error);
    }
  }

  /**
   * Delete specific cache key
   */
  async delete(key: string): Promise<void> {
    try {
      await deleteCache(key);
      this.memoryCache.delete(key);
      logger.debug(`Cache deleted: ${key}`);
    } catch (error: any) {
      logger.error(`Cache delete error for key ${key}:`, error);
    }
  }

  /**
   * Delete cache keys matching pattern
   */
  async deletePattern(pattern: string): Promise<void> {
    try {
      await deleteCachePattern(pattern);
      
      // Clean memory cache with pattern matching
      for (const key of this.memoryCache.keys()) {
        if (this.matchesPattern(key, pattern)) {
          this.memoryCache.delete(key);
        }
      }

      logger.debug(`Cache pattern deleted: ${pattern}`);
    } catch (error) {
      logger.error(`Cache delete pattern error for ${pattern}:`, error);
    }
  }

  /**
   * Get or set cache with a function
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttlSeconds?: number
  ): Promise<T> {
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const data = await fetchFunction();
    await this.set(key, data, ttlSeconds);
    return data;
  }

  /**
   * Invalidate all YouTube-related caches
   */
  async invalidateYouTubeCache(): Promise<void> {
    try {
      await Promise.all([
        this.deletePattern(`${CACHE_KEYS.YOUTUBE_CHANNEL}*`),
        this.deletePattern(`${CACHE_KEYS.YOUTUBE_SEARCH}*`)
      ]);
      logger.info('YouTube cache invalidated');
    } catch (error) {
      logger.error('Failed to invalidate YouTube cache:', error);
    }
  }

  /**
   * Invalidate user-specific caches
   */
  async invalidateUserCache(userId: string): Promise<void> {
    try {
      await Promise.all([
        this.deletePattern(`${CACHE_KEYS.CHANNEL_LISTS}${userId}*`),
        this.deletePattern(`${CACHE_KEYS.USER_ACTIVITY}${userId}*`)
      ]);
      logger.info(`User cache invalidated for user: ${userId}`);
    } catch (error) {
      logger.error(`Failed to invalidate user cache for ${userId}:`, error);
    }
  }

  /**
   * Clean up expired memory cache entries
   */
  cleanupMemoryCache(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, value] of this.memoryCache.entries()) {
      if (value.expires <= now) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug(`Cleaned up ${cleanedCount} expired memory cache entries`);
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    memorySize: number;
    redisConnected: boolean;
    memoryKeys: string[];
  }> {
    const redisClient = getRedisClient();
    
    return {
      memorySize: this.memoryCache.size,
      redisConnected: redisClient !== null,
      memoryKeys: Array.from(this.memoryCache.keys())
    };
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmupCache(): Promise<void> {
    try {
      logger.info('Starting cache warmup...');
      
      // This could be expanded to pre-load frequently accessed data
      // For now, we'll just log that warmup is available
      
      logger.info('Cache warmup completed');
    } catch (error) {
      logger.error('Cache warmup failed:', error);
    }
  }

  /**
   * Simple pattern matching for cache keys
   */
  private matchesPattern(key: string, pattern: string): boolean {
    // Convert glob pattern to regex
    const regexPattern = pattern
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    
    return new RegExp(`^${regexPattern}$`).test(key);
  }
}

// Create singleton instance
export const cacheService = new CacheService();

// Set up periodic memory cache cleanup
setInterval(() => {
  cacheService.cleanupMemoryCache();
}, 5 * 60 * 1000); // Every 5 minutes
