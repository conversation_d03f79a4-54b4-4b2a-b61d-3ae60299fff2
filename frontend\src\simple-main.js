// Modern YouTube Content Manager Interface
const root = document.getElementById('root');
if (root) {
  root.innerHTML = `
    <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <!-- Navigation Sidebar -->
      <nav class="fixed left-0 top-0 h-full w-64 bg-white/10 backdrop-blur-xl border-r border-white/20 z-40">
        <div class="p-6">
          <!-- Logo -->
          <div class="flex items-center space-x-3 mb-8">
            <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-white">YT Manager</h1>
              <p class="text-xs text-white/60">Content Organizer</p>
            </div>
          </div>

          <!-- Navigation Menu -->
          <div class="space-y-2">
            <a href="#" onclick="showDashboard()" class="nav-item active">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v0a2 2 0 01-2 2H10a2 2 0 01-2-2v0z"/>
              </svg>
              Dashboard
            </a>
            <a href="#" onclick="showSearch()" class="nav-item">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
              Search Channels
            </a>
            <a href="#" onclick="showLists()" class="nav-item">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5m14 14H5"/>
              </svg>
              My Lists
            </a>
            <a href="#" onclick="showAnalytics()" class="nav-item">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              Analytics
            </a>
          </div>

          <!-- Settings at bottom -->
          <div class="absolute bottom-6 left-6 right-6">
            <a href="#" onclick="showSettings()" class="nav-item">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              Settings
            </a>
          </div>
        </div>
      </nav>

      <!-- Main Content Area -->
      <main class="ml-64 min-h-screen">
        <!-- Top Header -->
        <header class="bg-white/5 backdrop-blur-xl border-b border-white/10 sticky top-0 z-30">
          <div class="px-8 py-4">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-2xl font-bold text-white" id="pageTitle">Dashboard</h2>
                <p class="text-white/60 text-sm" id="pageSubtitle">Overview of your YouTube content management</p>
              </div>
              <div class="flex items-center space-x-4">
                <!-- Status Indicator -->
                <div class="flex items-center space-x-2 bg-green-500/20 text-green-400 px-3 py-1.5 rounded-full text-sm">
                  <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>Connected</span>
                </div>
                <!-- API Status -->
                <div class="flex items-center space-x-2 bg-yellow-500/20 text-yellow-400 px-3 py-1.5 rounded-full text-sm cursor-pointer" onclick="showApiSetup()">
                  <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>API Setup Required</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Dashboard Content -->
        <div class="p-8" id="mainContent">
          <!-- Stats Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Channels -->
            <div class="stat-card">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-white/60 text-sm font-medium">Total Channels</p>
                  <p class="text-3xl font-bold text-white mt-1">0</p>
                  <p class="text-green-400 text-sm mt-1">+0 this week</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V7H3a1 1 0 01-1-1V5a1 1 0 011-1h4z"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Total Lists -->
            <div class="stat-card">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-white/60 text-sm font-medium">Channel Lists</p>
                  <p class="text-3xl font-bold text-white mt-1">0</p>
                  <p class="text-green-400 text-sm mt-1">+0 this week</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5m14 14H5"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Total Subscribers -->
            <div class="stat-card">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-white/60 text-sm font-medium">Total Subscribers</p>
                  <p class="text-3xl font-bold text-white mt-1">0</p>
                  <p class="text-green-400 text-sm mt-1">Across all channels</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- API Quota -->
            <div class="stat-card">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-white/60 text-sm font-medium">API Quota Used</p>
                  <p class="text-3xl font-bold text-white mt-1">0%</p>
                  <p class="text-yellow-400 text-sm mt-1">10,000 daily limit</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Main Dashboard Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Welcome Section -->
            <div class="lg:col-span-2">
              <div class="glass-card p-8">
                <div class="flex items-center space-x-4 mb-6">
                  <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-2xl font-bold text-white">Welcome to YouTube Content Manager</h3>
                    <p class="text-white/60 mt-1">Your powerful tool for organizing and managing YouTube channels</p>
                  </div>
                </div>

                <!-- Feature Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                  <div class="feature-item">
                    <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mb-3">
                      <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                      </svg>
                    </div>
                    <h4 class="text-white font-semibold mb-1">Smart Search</h4>
                    <p class="text-white/60 text-sm">Find YouTube channels with advanced filters and real-time results</p>
                  </div>
                  <div class="feature-item">
                    <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mb-3">
                      <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5m14 14H5"/>
                      </svg>
                    </div>
                    <h4 class="text-white font-semibold mb-1">Custom Lists</h4>
                    <p class="text-white/60 text-sm">Organize channels into personalized collections and categories</p>
                  </div>
                  <div class="feature-item">
                    <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center mb-3">
                      <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                      </svg>
                    </div>
                    <h4 class="text-white font-semibold mb-1">Analytics</h4>
                    <p class="text-white/60 text-sm">Track subscriber growth, video counts, and engagement metrics</p>
                  </div>
                  <div class="feature-item">
                    <div class="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center mb-3">
                      <svg class="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                      </svg>
                    </div>
                    <h4 class="text-white font-semibold mb-1">Real-time Updates</h4>
                    <p class="text-white/60 text-sm">Get live updates on channel statistics and new content</p>
                  </div>
                </div>

                <!-- CTA Button -->
                <button
                  onclick="showApiSetup()"
                  class="btn-primary w-full sm:w-auto"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                  </svg>
                  Setup YouTube API Key
                </button>
              </div>
            </div>

            <!-- Sidebar Content -->
            <div class="space-y-6">
              <!-- System Status -->
              <div class="glass-card p-6">
                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                  <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  System Status
                </h3>
                <div class="space-y-4">
                  <div class="status-item">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-white font-medium">Backend API</span>
                      </div>
                      <span class="text-green-400 text-sm font-medium">Online</span>
                    </div>
                    <div class="mt-1 text-xs text-white/60">Last checked: Just now</div>
                  </div>
                  <div class="status-item">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-white font-medium">Database</span>
                      </div>
                      <span class="text-green-400 text-sm font-medium">Connected</span>
                    </div>
                    <div class="mt-1 text-xs text-white/60">SQLite • 0 records</div>
                  </div>
                  <div class="status-item">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                        <span class="text-white font-medium">YouTube API</span>
                      </div>
                      <span class="text-yellow-400 text-sm font-medium">Setup Required</span>
                    </div>
                    <div class="mt-1 text-xs text-white/60">Configure API key to start</div>
                  </div>
                </div>
              </div>

              <!-- Quick Actions -->
              <div class="glass-card p-6">
                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                  <svg class="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                  Quick Actions
                </h3>
                <div class="space-y-3">
                  <button
                    onclick="testBackend()"
                    class="action-btn"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"/>
                    </svg>
                    Test Connection
                  </button>
                  <button
                    onclick="showApiSetup()"
                    class="action-btn"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                    </svg>
                    Configure API
                  </button>
                  <button
                    onclick="showSearch()"
                    class="action-btn"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                    Search Channels
                  </button>
                  <button
                    onclick="createNewList()"
                    class="action-btn"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    Create List
                  </button>
                </div>
              </div>

              <!-- Recent Activity -->
              <div class="glass-card p-6">
                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                  <svg class="w-5 h-5 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Recent Activity
                </h3>
                <div class="text-center py-8">
                  <div class="w-16 h-16 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg class="w-8 h-8 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <p class="text-white/60 text-sm">No recent activity</p>
                  <p class="text-white/40 text-xs mt-1">Start managing channels to see activity here</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API Setup Modal -->
        <div id="apiModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm hidden items-center justify-center z-50 p-4">
          <div class="bg-white/10 backdrop-blur-xl rounded-2xl p-8 max-w-lg w-full border border-white/20 shadow-2xl">
            <div class="flex items-center space-x-3 mb-6">
              <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-2xl font-bold text-white">YouTube API Setup</h3>
                <p class="text-white/60">Configure your API access</p>
              </div>
            </div>

            <div class="space-y-6">
              <div>
                <label class="block text-sm font-semibold text-white mb-3">API Key</label>
                <input
                  type="text"
                  id="apiKeyInput"
                  placeholder="Enter your YouTube Data API v3 key"
                  class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                />
              </div>

              <div class="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
                <h4 class="text-blue-300 font-semibold mb-2 flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  How to get your API key:
                </h4>
                <ol class="text-blue-200 text-sm space-y-1 list-decimal list-inside">
                  <li>Visit <a href="https://console.cloud.google.com/" target="_blank" class="text-blue-300 hover:text-blue-200 underline">Google Cloud Console</a></li>
                  <li>Create a new project or select existing</li>
                  <li>Enable "YouTube Data API v3"</li>
                  <li>Go to Credentials → Create Credentials → API Key</li>
                  <li>Copy and paste the key above</li>
                </ol>
              </div>

              <div class="flex space-x-3">
                <button
                  onclick="saveApiKey()"
                  class="flex-1 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white px-6 py-3 rounded-xl font-semibold transition-all transform hover:scale-105"
                >
                  Save API Key
                </button>
                <button
                  onclick="closeApiSetup()"
                  class="flex-1 bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-xl font-semibold transition-all border border-white/20"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Custom Styles -->
    <style>
      /* Navigation Styles */
      .nav-item {
        display: flex;
        align-items: center;
        space-x: 0.75rem;
        padding: 0.75rem 1rem;
        color: rgba(255, 255, 255, 0.6);
        border-radius: 0.75rem;
        transition: all 0.2s;
        text-decoration: none;
        font-weight: 500;
      }

      .nav-item:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transform: translateX(4px);
      }

      .nav-item.active {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(236, 72, 153, 0.2));
        color: white;
        border: 1px solid rgba(239, 68, 68, 0.3);
      }

      /* Glass Card Effect */
      .glass-card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }

      /* Stat Cards */
      .stat-card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 1.5rem;
        padding: 1.5rem;
        transition: all 0.3s;
      }

      .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.2);
      }

      /* Feature Items */
      .feature-item {
        padding: 1rem;
        border-radius: 1rem;
        transition: all 0.2s;
      }

      .feature-item:hover {
        background: rgba(255, 255, 255, 0.05);
        transform: translateY(-2px);
      }

      /* Status Items */
      .status-item {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      }

      .status-item:last-child {
        border-bottom: none;
      }

      /* Action Buttons */
      .action-btn {
        display: flex;
        align-items: center;
        space-x: 0.75rem;
        width: 100%;
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 0.75rem;
        color: white;
        font-weight: 500;
        transition: all 0.2s;
        text-align: left;
      }

      .action-btn:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      /* Primary Button */
      .btn-primary {
        display: inline-flex;
        align-items: center;
        padding: 0.875rem 2rem;
        background: linear-gradient(135deg, #ef4444, #ec4899);
        color: white;
        font-weight: 600;
        border-radius: 0.75rem;
        border: none;
        transition: all 0.3s;
        box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(239, 68, 68, 0.4);
        background: linear-gradient(135deg, #dc2626, #db2777);
      }

      /* Animations */
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }

      .glass-card {
        animation: fadeIn 0.6s ease-out;
      }

      .stat-card {
        animation: fadeIn 0.6s ease-out;
      }

      /* Responsive Design */
      @media (max-width: 1024px) {
        nav {
          transform: translateX(-100%);
          transition: transform 0.3s;
        }

        nav.open {
          transform: translateX(0);
        }

        main {
          margin-left: 0;
        }
      }
    </style>
  `;
}

// Enhanced JavaScript functions for modern interface
window.currentPage = 'dashboard';

// Navigation Functions
window.showDashboard = function() {
  setActivePage('dashboard', 'Dashboard', 'Overview of your YouTube content management');
  updateNavigation('dashboard');
  // Dashboard content is already loaded
}

window.showSearch = function() {
  setActivePage('search', 'Search Channels', 'Find and discover YouTube channels');
  updateNavigation('search');
  document.getElementById('mainContent').innerHTML = `
    <div class="glass-card p-8">
      <div class="flex items-center space-x-4 mb-6">
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white">Search YouTube Channels</h3>
      </div>
      <div class="text-center py-12">
        <p class="text-white/60 mb-4">Search functionality will be available after API setup</p>
        <button onclick="showApiSetup()" class="btn-primary">Setup API First</button>
      </div>
    </div>
  `;
}

window.showLists = function() {
  setActivePage('lists', 'My Lists', 'Manage your channel collections');
  updateNavigation('lists');
  document.getElementById('mainContent').innerHTML = `
    <div class="glass-card p-8">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5m14 14H5"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-white">My Channel Lists</h3>
        </div>
        <button onclick="createNewList()" class="btn-primary">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Create List
        </button>
      </div>
      <div class="text-center py-12">
        <p class="text-white/60 mb-4">No channel lists created yet</p>
        <p class="text-white/40 text-sm">Create your first list to organize YouTube channels</p>
      </div>
    </div>
  `;
}

window.showAnalytics = function() {
  setActivePage('analytics', 'Analytics', 'Track your channel performance');
  updateNavigation('analytics');
  document.getElementById('mainContent').innerHTML = `
    <div class="glass-card p-8">
      <div class="flex items-center space-x-4 mb-6">
        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white">Analytics Dashboard</h3>
      </div>
      <div class="text-center py-12">
        <p class="text-white/60 mb-4">Analytics will be available after adding channels</p>
        <button onclick="showSearch()" class="btn-primary">Start Adding Channels</button>
      </div>
    </div>
  `;
}

window.showSettings = function() {
  setActivePage('settings', 'Settings', 'Configure your application preferences');
  updateNavigation('settings');
  document.getElementById('mainContent').innerHTML = `
    <div class="glass-card p-8">
      <div class="flex items-center space-x-4 mb-6">
        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white">Settings</h3>
      </div>
      <div class="space-y-6">
        <div class="bg-white/5 rounded-xl p-6">
          <h4 class="text-white font-semibold mb-4">API Configuration</h4>
          <button onclick="showApiSetup()" class="action-btn">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
            </svg>
            Configure YouTube API Key
          </button>
        </div>
      </div>
    </div>
  `;
}

// Helper Functions
function setActivePage(page, title, subtitle) {
  window.currentPage = page;
  document.getElementById('pageTitle').textContent = title;
  document.getElementById('pageSubtitle').textContent = subtitle;
}

function updateNavigation(activePage) {
  document.querySelectorAll('.nav-item').forEach(item => {
    item.classList.remove('active');
  });

  const navItems = {
    'dashboard': 0,
    'search': 1,
    'lists': 2,
    'analytics': 3,
    'settings': 4
  };

  const activeIndex = navItems[activePage];
  if (activeIndex !== undefined) {
    document.querySelectorAll('.nav-item')[activeIndex].classList.add('active');
  }
}

// Modal Functions
window.showApiSetup = function() {
  document.getElementById('apiModal').classList.remove('hidden');
  document.getElementById('apiModal').classList.add('flex');
  document.getElementById('apiKeyInput').focus();
}

window.closeApiSetup = function() {
  document.getElementById('apiModal').classList.add('hidden');
  document.getElementById('apiModal').classList.remove('flex');
}

window.saveApiKey = function() {
  const apiKey = document.getElementById('apiKeyInput').value;
  if (apiKey.trim()) {
    // Here you would normally save to backend
    showNotification('API Key saved successfully!', 'success');
    closeApiSetup();
    // Update API status in UI
    updateApiStatus('connected');
  } else {
    showNotification('Please enter a valid API key', 'error');
  }
}

// Utility Functions
window.testBackend = async function() {
  try {
    const response = await fetch('http://localhost:3001/health');
    const data = await response.json();
    showNotification('Backend connection successful!', 'success');
    console.log('Backend response:', data);
  } catch (error) {
    showNotification('Backend connection failed: ' + error.message, 'error');
  }
}

window.createNewList = function() {
  showNotification('Create list functionality coming soon!', 'info');
}

function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 z-50 p-4 rounded-xl shadow-lg transform transition-all duration-300 translate-x-full`;

  const colors = {
    success: 'bg-green-500/20 border border-green-500/30 text-green-300',
    error: 'bg-red-500/20 border border-red-500/30 text-red-300',
    info: 'bg-blue-500/20 border border-blue-500/30 text-blue-300'
  };

  notification.className += ` ${colors[type]}`;
  notification.textContent = message;

  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.classList.remove('translate-x-full');
  }, 100);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.classList.add('translate-x-full');
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 3000);
}

function updateApiStatus(status) {
  // Update API status indicators in the UI
  const statusElements = document.querySelectorAll('[data-api-status]');
  statusElements.forEach(el => {
    if (status === 'connected') {
      el.innerHTML = `
        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
        <span class="text-green-400 text-sm font-medium">Connected</span>
      `;
    }
  });
}
