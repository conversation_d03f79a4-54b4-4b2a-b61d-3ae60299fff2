import cron from 'node-cron';
import { logger } from '@/utils/logger';
import { activityService } from '@/services/activityService';
import { youtubeService } from '@/services/youtubeService';
import { prisma } from '@/utils/database';

/**
 * Start background jobs for periodic tasks
 */
export function startBackgroundJobs() {
  logger.info('Starting background jobs...');

  // Update channel statistics every 6 hours
  const channelUpdateInterval = process.env.CHANNEL_UPDATE_INTERVAL || '0 */6 * * *';
  
  cron.schedule(channelUpdateInterval, async () => {
    logger.info('Starting scheduled channel update job');
    try {
      // TODO: Implement channel update logic
      await updateChannelStatistics();
      logger.info('Channel update job completed successfully');
    } catch (error) {
      logger.error('Channel update job failed:', error);
    }
  });

  // Clean up old activity logs daily at midnight
  cron.schedule('0 0 * * *', async () => {
    logger.info('Starting activity log cleanup job');
    try {
      await cleanupOldActivityLogs();
      logger.info('Activity log cleanup job completed successfully');
    } catch (error) {
      logger.error('Activity log cleanup job failed:', error);
    }
  });

  logger.info('Background jobs scheduled successfully');
}

/**
 * Update statistics for all saved channels
 */
async function updateChannelStatistics() {
  try {
    logger.info('Starting channel statistics update...');

    // Get all unique channels from the database
    const channels = await prisma.youTubeChannel.findMany({
      select: {
        id: true,
        channelId: true,
        title: true,
        lastUpdated: true
      },
      orderBy: { lastUpdated: 'asc' }
    });

    if (channels.length === 0) {
      logger.info('No channels found to update');
      return;
    }

    let updatedCount = 0;
    let errorCount = 0;

    // Process channels in batches to respect API limits
    const batchSize = 10;
    for (let i = 0; i < channels.length; i += batchSize) {
      const batch = channels.slice(i, i + batchSize);
      const channelIds = batch.map(c => c.channelId);

      try {
        // Get updated channel data from YouTube API
        const updatedChannels = await youtubeService.getChannelsByIds(channelIds);

        // Update each channel in the database
        for (const updatedChannel of updatedChannels) {
          try {
            await prisma.youTubeChannel.update({
              where: { channelId: updatedChannel.channelId },
              data: {
                title: updatedChannel.title,
                description: updatedChannel.description,
                thumbnailUrl: updatedChannel.thumbnailUrl,
                subscriberCount: updatedChannel.subscriberCount,
                videoCount: updatedChannel.videoCount,
                viewCount: updatedChannel.viewCount,
                lastUpdated: new Date()
              }
            });
            updatedCount++;
          } catch (updateError) {
            logger.error(`Failed to update channel ${updatedChannel.channelId}:`, updateError);
            errorCount++;
          }
        }

        // Add delay between batches to respect rate limits
        if (i + batchSize < channels.length) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
        }

      } catch (batchError) {
        logger.error(`Failed to fetch batch starting at index ${i}:`, batchError);
        errorCount += batch.length;
      }
    }

    logger.info(`Channel statistics update completed. Updated: ${updatedCount}, Errors: ${errorCount}`);

  } catch (error) {
    logger.error('Failed to update channel statistics:', error);
  }
}

/**
 * Clean up activity logs older than 90 days
 */
async function cleanupOldActivityLogs() {
  try {
    const deletedCount = await activityService.cleanupOldActivities(90);
    logger.info(`Cleaned up ${deletedCount} old activity logs`);
  } catch (error) {
    logger.error('Failed to cleanup old activity logs:', error);
  }
}
