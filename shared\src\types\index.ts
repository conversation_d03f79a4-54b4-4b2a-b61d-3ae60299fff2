// User types
export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

// Channel types
export interface YouTubeChannel {
  id: string;
  channelId: string; // YouTube channel ID
  title: string;
  description: string;
  thumbnailUrl: string;
  subscriberCount: number;
  videoCount: number;
  viewCount: number;
  publishedAt: Date;
  customUrl?: string;
  country?: string;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Channel List types
export interface ChannelList {
  id: string;
  userId: string;
  name: string;
  description?: string;
  color: string;
  isPublic: boolean;
  channelCount: number;
  createdAt: Date;
  updatedAt: Date;
  channels?: YouTubeChannel[];
}

// Activity Log types
export enum ActivityType {
  CHANNEL_ADDED = 'CHANNEL_ADDED',
  CHANNEL_REMOVED = 'CHANNEL_REMOVED',
  LIST_CREATED = 'LIST_CREATED',
  LIST_UPDATED = 'LIST_UPDATED',
  LIST_DELETED = 'LIST_DELETED',
  LIST_RENAMED = 'LIST_RENAMED',
  CHANNEL_MOVED = 'CHANNEL_MOVED'
}

export interface ActivityLog {
  id: string;
  userId: string;
  type: ActivityType;
  description: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// YouTube API types
export interface YouTubeSearchFilters {
  query?: string;
  minSubscribers?: number;
  maxSubscribers?: number;
  minViews?: number;
  maxViews?: number;
  country?: string;
  publishedAfter?: Date;
  publishedBefore?: Date;
  order?: 'relevance' | 'date' | 'rating' | 'viewCount' | 'title';
}

export interface YouTubeChannelStats {
  subscriberCount: number;
  videoCount: number;
  viewCount: number;
  lastVideoDate?: Date;
  averageViews?: number;
  uploadFrequency?: string;
}

// Frontend specific types
export interface ViewMode {
  type: 'grid' | 'list' | 'detailed';
  columns?: number;
}

export interface DashboardState {
  selectedListId?: string;
  viewMode: ViewMode;
  searchQuery: string;
  filters: YouTubeSearchFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

export enum ErrorCode {
  YOUTUBE_API_QUOTA_EXCEEDED = 'YOUTUBE_API_QUOTA_EXCEEDED',
  YOUTUBE_API_ERROR = 'YOUTUBE_API_ERROR',
  CHANNEL_NOT_FOUND = 'CHANNEL_NOT_FOUND',
  LIST_NOT_FOUND = 'LIST_NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR'
}
