import { useState } from 'react'
import { useParams } from 'react-router-dom'
import { ArrowLeft, Plus, Search, MoreHorizontal } from 'lucide-react'
import { ChannelListCard } from '@/components/ChannelList/ChannelListCard'
import { ChannelCard } from '@/components/Channel/ChannelCard'
import { CreateListModal } from '@/components/ChannelList/CreateListModal'

// Mock data - same as Dashboard
const mockChannelLists = [
  {
    id: '1',
    name: 'YouTube Education (All)',
    description: 'Educational channels covering various topics',
    channelCount: 32,
    color: '#06B6D4',
    lastUpdated: '2 days ago',
    channels: [
      { id: '1', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Khan Academy' },
      { id: '2', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Crash Course' },
      { id: '3', thumbnailUrl: 'https://via.placeholder.com/40', title: 'TED-Ed' },
      { id: '4', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Veritasium' },
    ]
  },
  // ... other lists
]

// Mock detailed channels for a specific list
const mockDetailedChannels = [
  {
    id: '1',
    channelId: 'UC_x5XG1OV2P6uZZ5FSM9Ttw',
    title: 'Khan Academy',
    description: 'Learn anything, anywhere. Khan Academy is a nonprofit with the mission of providing a free, world-class education for anyone, anywhere.',
    thumbnailUrl: 'https://via.placeholder.com/120',
    subscriberCount: 7800000,
    videoCount: 8234,
    viewCount: 1234567890,
    publishedAt: new Date('2006-10-16'),
    customUrl: '@khanacademy',
    country: 'US',
    lastUpdated: new Date()
  },
  {
    id: '2',
    channelId: 'UCX6OQ3DkcsbYNE6H8uQQuVA',
    title: 'Crash Course',
    description: 'Crash Course is one of the best ways to educate yourself, your classmates, and your family on YouTube!',
    thumbnailUrl: 'https://via.placeholder.com/120',
    subscriberCount: 14200000,
    videoCount: 1456,
    viewCount: 987654321,
    publishedAt: new Date('2011-05-19'),
    customUrl: '@crashcourse',
    country: 'US',
    lastUpdated: new Date()
  }
]

export function ChannelLists() {
  const { listId } = useParams()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // If listId is provided, show detailed view of that list
  if (listId) {
    const currentList = mockChannelLists.find(list => list.id === listId)
    
    if (!currentList) {
      return (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-white mb-2">List Not Found</h2>
          <p className="text-slate-400">The requested channel list could not be found.</p>
        </div>
      )
    }

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <button 
            onClick={() => window.history.back()}
            className="p-2 text-slate-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <div 
                className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-xl"
                style={{ backgroundColor: currentList.color }}
              >
                {currentList.name.charAt(0)}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">{currentList.name}</h1>
                <p className="text-slate-400">{currentList.channelCount} channels</p>
              </div>
            </div>
            {currentList.description && (
              <p className="text-slate-400">{currentList.description}</p>
            )}
          </div>
          <button className="p-2 text-slate-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors">
            <MoreHorizontal className="w-5 h-5" />
          </button>
        </div>

        {/* Search and Actions */}
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search channels in this list..."
              className="input w-full pl-10"
            />
          </div>
          <button className="btn-primary btn-md">
            <Plus className="w-4 h-4 mr-2" />
            Add Channel
          </button>
        </div>

        {/* Channels Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockDetailedChannels
            .filter(channel => 
              channel.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              channel.description.toLowerCase().includes(searchQuery.toLowerCase())
            )
            .map((channel) => (
              <ChannelCard
                key={channel.id}
                channel={channel}
                showAddButton={false}
              />
            ))}
        </div>
      </div>
    )
  }

  // Default view - show all channel lists
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Channel Lists</h1>
          <p className="text-slate-400 mt-1">Organize your favorite channels into custom lists</p>
        </div>
        
        <button 
          onClick={() => setShowCreateModal(true)}
          className="btn-primary btn-md"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create List
        </button>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search lists..."
          className="input w-full pl-10"
        />
      </div>

      {/* Channel Lists Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockChannelLists
          .filter(list => 
            list.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (list.description && list.description.toLowerCase().includes(searchQuery.toLowerCase()))
          )
          .map((list) => (
            <ChannelListCard
              key={list.id}
              list={list}
              viewMode="grid"
            />
          ))}
        
        {/* Add New Channel List Card */}
        <div 
          onClick={() => setShowCreateModal(true)}
          className="card border-2 border-dashed border-dark-600 hover:border-primary-500 transition-colors cursor-pointer group min-h-[200px] flex items-center justify-center"
        >
          <div className="text-center">
            <Plus className="w-8 h-8 text-slate-400 group-hover:text-primary-500 mx-auto mb-2" />
            <p className="text-slate-400 group-hover:text-primary-500 font-medium">
              Add New Channel List
            </p>
          </div>
        </div>
      </div>

      {/* Create List Modal */}
      <CreateListModal 
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  )
}
