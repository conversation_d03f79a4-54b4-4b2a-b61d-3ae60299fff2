{
  service: 'youtube-manager-backend',
  name: 'PrismaClientInitializationError',
  clientVersion: '5.22.0',
  errorCode: 'P1001',
  level: 'error',
  message: "Failed to connect to database: Can't reach database server at `localhost:5432`\n" +
    '\n' +
    'Please make sure your database server is running at `localhost:5432`.',
  stack: "PrismaClientInitializationError: Can't reach database server at `localhost:5432`\n" +
    '\n' +
    'Please make sure your database server is running at `localhost:5432`.\n' +
    '    at t (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n' +
    '    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\utils\\database.ts:50:5)\n' +
    '    at async startServer (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\index.ts:99:5)',
  timestamp: '2025-06-18 00:31:11'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204882100,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:01:33 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:01:22',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204896090,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:01:47 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:01:36',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204914492,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:02:05 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:01:54',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
{
  service: 'youtube-manager-backend',
  config: {
    url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
    method: 'GET',
    userAgentDirectives: [
      {
        product: 'google-api-nodejs-client',
        version: '7.2.0',
        comment: 'gzip'
      }
    ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      part: [ 'snippet' ],
      mine: false,
      id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
      key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [ 'GET', 'HEAD', 'PUT', 'OPTIONS', 'DELETE' ],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1750204952283,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [ [ 100, 199 ], [ 408, 408 ], [ 429, 429 ], [ 500, 599 ] ]
    }
  },
  response: {
    config: {
      url: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w',
      method: 'GET',
      userAgentDirectives: [
        {
          product: 'google-api-nodejs-client',
          version: '7.2.0',
          comment: 'gzip'
        }
      ],
      paramsSerializer: [Function (anonymous)],
      headers: {
        'x-goog-api-client': 'gdcl/7.2.0 gl-node/22.15.1',
        'Accept-Encoding': 'gzip',
        'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
      },
      params: {
        part: [ 'snippet' ],
        mine: false,
        id: [ 'UCuAXFkgsw1L7xaCfnd5JJOw' ],
        key: 'AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
      },
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: {
      error: {
        code: 400,
        message: "The request's use of the <code>mine</code> parameter is not supported.",
        errors: [
          {
            message: "The request's use of the <code>mine</code> parameter is not supported.",
            domain: 'youtube.parameter',
            reason: 'invalidMine',
            location: 'mine',
            locationType: 'parameter'
          }
        ]
      }
    },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 18 Jun 2025 00:02:43 GMT',
      server: 'scaffolding on HTTPServer2',
      'transfer-encoding': 'chunked',
      vary: 'Origin, X-Origin, Referer',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://youtube.googleapis.com/youtube/v3/channels?part=snippet&mine=false&id=UCuAXFkgsw1L7xaCfnd5JJOw&key=AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: "The request's use of the <code>mine</code> parameter is not supported.",
      domain: 'youtube.parameter',
      reason: 'invalidMine',
      location: 'mine',
      locationType: 'parameter'
    }
  ],
  level: 'error',
  message: "API key test failed: The request's use of the <code>mine</code> parameter is not supported.",
  stack: "Error: The request's use of the <code>mine</code> parameter is not supported.\n" +
    '    at Gaxios._request (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\node_modules\\gaxios\\src\\gaxios.ts:146:15)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async YouTubeService.testApiKey (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\services\\youtubeService.ts:37:24)\n' +
    '    at async <anonymous> (C:\\Users\\<USER>\\OneDrive\\Documents\\vscode\\newproj\\backend\\src\\routes\\youtube.ts:35:21)',
  timestamp: '2025-06-18 01:02:32',
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
