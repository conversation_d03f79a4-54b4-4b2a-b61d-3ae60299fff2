"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.youtubeService = exports.YouTubeService = void 0;
const googleapis_1 = require("googleapis");
const logger_1 = require("@/utils/logger");
const redis_1 = require("@/utils/redis");
const database_1 = require("@/utils/database");
const errorHandler_1 = require("@/middleware/errorHandler");
const shared_1 = require("@/types/shared");
class YouTubeService {
    constructor() {
        this.apiKey = process.env.YOUTUBE_API_KEY;
        if (!this.apiKey) {
            throw new Error('YOUTUBE_API_KEY environment variable is required');
        }
        this.youtube = googleapis_1.google.youtube({
            version: 'v3',
            auth: this.apiKey
        });
    }
    /**
     * Search for YouTube channels based on filters
     */
    async searchChannels(filters, maxResults = 25) {
        const cacheKey = `${shared_1.CACHE_KEYS.YOUTUBE_SEARCH}${JSON.stringify(filters)}_${maxResults}`;
        try {
            // Check cache first
            const cachedResult = await (0, redis_1.getCache)(cacheKey);
            if (cachedResult) {
                logger_1.logger.debug('YouTube search cache hit');
                return cachedResult;
            }
            // Check quota before making API call
            await this.checkAndUpdateQuota(shared_1.YOUTUBE_API.QUOTA_COSTS.SEARCH);
            const searchParams = {
                part: ['snippet'],
                type: ['channel'],
                maxResults: Math.min(maxResults, shared_1.YOUTUBE_API.MAX_RESULTS_PER_REQUEST),
                order: filters.order || 'relevance',
                q: filters.query || ''
            };
            if (filters.publishedAfter) {
                searchParams.publishedAfter = filters.publishedAfter.toISOString();
            }
            if (filters.publishedBefore) {
                searchParams.publishedBefore = filters.publishedBefore.toISOString();
            }
            if (filters.country) {
                searchParams.regionCode = filters.country;
            }
            const response = await this.youtube.search.list(searchParams);
            if (!response.data.items) {
                return { channels: [], totalResults: 0 };
            }
            // Get detailed channel information
            const channelIds = response.data.items
                .map(item => item.snippet?.channelId)
                .filter(Boolean);
            const channelsData = await this.getChannelsByIds(channelIds);
            // Apply subscriber and view filters
            const filteredChannels = channelsData.filter(channel => {
                if (filters.minSubscribers && channel.subscriberCount < filters.minSubscribers) {
                    return false;
                }
                if (filters.maxSubscribers && channel.subscriberCount > filters.maxSubscribers) {
                    return false;
                }
                if (filters.minViews && channel.viewCount < filters.minViews) {
                    return false;
                }
                if (filters.maxViews && channel.viewCount > filters.maxViews) {
                    return false;
                }
                return true;
            });
            const result = {
                channels: filteredChannels,
                totalResults: response.data.pageInfo?.totalResults || 0,
                nextPageToken: response.data.nextPageToken
            };
            // Cache the result
            await (0, redis_1.setCache)(cacheKey, result, shared_1.CACHE_TTL.YOUTUBE_SEARCH);
            logger_1.logger.info(`YouTube search completed: ${filteredChannels.length} channels found`);
            return result;
        }
        catch (error) {
            logger_1.logger.error('YouTube search error:', error);
            if (error.code === 403) {
                throw (0, errorHandler_1.createError)('YouTube API quota exceeded', 429, shared_1.ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED);
            }
            throw (0, errorHandler_1.createError)('YouTube API search failed', 500, shared_1.ErrorCode.YOUTUBE_API_ERROR);
        }
    }
    /**
     * Get detailed information for specific channel IDs
     */
    async getChannelsByIds(channelIds) {
        if (channelIds.length === 0)
            return [];
        const cacheKey = `${shared_1.CACHE_KEYS.YOUTUBE_CHANNEL}${channelIds.sort().join(',')}`;
        try {
            // Check cache first
            const cachedResult = await (0, redis_1.getCache)(cacheKey);
            if (cachedResult) {
                logger_1.logger.debug('YouTube channels cache hit');
                return cachedResult;
            }
            // Check quota before making API call
            await this.checkAndUpdateQuota(shared_1.YOUTUBE_API.QUOTA_COSTS.CHANNELS);
            const response = await this.youtube.channels.list({
                part: ['snippet', 'statistics', 'brandingSettings'],
                id: channelIds
            });
            if (!response.data.items) {
                return [];
            }
            const channels = response.data.items.map(item => ({
                channelId: item.id,
                title: item.snippet?.title || '',
                description: item.snippet?.description || '',
                thumbnailUrl: item.snippet?.thumbnails?.high?.url ||
                    item.snippet?.thumbnails?.medium?.url ||
                    item.snippet?.thumbnails?.default?.url || '',
                subscriberCount: parseInt(item.statistics?.subscriberCount || '0'),
                videoCount: parseInt(item.statistics?.videoCount || '0'),
                viewCount: parseInt(item.statistics?.viewCount || '0'),
                publishedAt: new Date(item.snippet?.publishedAt || Date.now()),
                customUrl: item.snippet?.customUrl,
                country: item.snippet?.country,
                lastUpdated: new Date()
            }));
            // Cache the result
            await (0, redis_1.setCache)(cacheKey, channels, shared_1.CACHE_TTL.YOUTUBE_CHANNEL);
            logger_1.logger.info(`Retrieved ${channels.length} channel details from YouTube API`);
            return channels;
        }
        catch (error) {
            logger_1.logger.error('YouTube channels fetch error:', error);
            if (error.code === 403) {
                throw (0, errorHandler_1.createError)('YouTube API quota exceeded', 429, shared_1.ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED);
            }
            throw (0, errorHandler_1.createError)('YouTube API channels fetch failed', 500, shared_1.ErrorCode.YOUTUBE_API_ERROR);
        }
    }
    /**
     * Get a single channel by ID with caching
     */
    async getChannelById(channelId) {
        const channels = await this.getChannelsByIds([channelId]);
        return channels[0] || null;
    }
    /**
     * Get channel statistics and analytics
     */
    async getChannelStats(channelId) {
        const cacheKey = `${shared_1.CACHE_KEYS.YOUTUBE_CHANNEL}stats_${channelId}`;
        try {
            // Check cache first
            const cachedStats = await (0, redis_1.getCache)(cacheKey);
            if (cachedStats) {
                logger_1.logger.debug('YouTube channel stats cache hit');
                return cachedStats;
            }
            // Get basic channel info
            const channel = await this.getChannelById(channelId);
            if (!channel) {
                throw (0, errorHandler_1.createError)('Channel not found', 404, shared_1.ErrorCode.CHANNEL_NOT_FOUND);
            }
            // Get recent videos to calculate upload frequency and average views
            const videosResponse = await this.youtube.search.list({
                part: ['snippet'],
                channelId: channelId,
                type: ['video'],
                order: 'date',
                maxResults: 10
            });
            let lastVideoDate;
            let uploadFrequency = 'Unknown';
            if (videosResponse.data.items && videosResponse.data.items.length > 0) {
                const videos = videosResponse.data.items;
                lastVideoDate = new Date(videos[0].snippet?.publishedAt || Date.now());
                // Calculate upload frequency based on recent videos
                if (videos.length >= 2) {
                    const firstVideo = new Date(videos[videos.length - 1].snippet?.publishedAt || Date.now());
                    const daysDiff = Math.floor((lastVideoDate.getTime() - firstVideo.getTime()) / (1000 * 60 * 60 * 24));
                    const avgDaysBetweenVideos = daysDiff / (videos.length - 1);
                    if (avgDaysBetweenVideos <= 1) {
                        uploadFrequency = 'Daily';
                    }
                    else if (avgDaysBetweenVideos <= 7) {
                        uploadFrequency = 'Weekly';
                    }
                    else if (avgDaysBetweenVideos <= 30) {
                        uploadFrequency = 'Monthly';
                    }
                    else {
                        uploadFrequency = 'Irregular';
                    }
                }
            }
            const stats = {
                subscriberCount: channel.subscriberCount,
                videoCount: channel.videoCount,
                viewCount: channel.viewCount,
                lastVideoDate,
                uploadFrequency
            };
            // Cache the stats
            await (0, redis_1.setCache)(cacheKey, stats, shared_1.CACHE_TTL.YOUTUBE_CHANNEL);
            return stats;
        }
        catch (error) {
            logger_1.logger.error('YouTube channel stats error:', error);
            throw (0, errorHandler_1.createError)('Failed to get channel statistics', 500, shared_1.ErrorCode.YOUTUBE_API_ERROR);
        }
    }
    /**
     * Check and update API quota usage
     */
    async checkAndUpdateQuota(quotaCost) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        try {
            // Get or create today's quota record
            let quotaRecord = await database_1.prisma.youTubeApiQuota.findUnique({
                where: { date: today }
            });
            if (!quotaRecord) {
                quotaRecord = await database_1.prisma.youTubeApiQuota.create({
                    data: {
                        date: today,
                        quotaUsed: 0,
                        quotaLimit: shared_1.YOUTUBE_API.DAILY_QUOTA_LIMIT
                    }
                });
            }
            // Check if adding this request would exceed quota
            if (quotaRecord.quotaUsed + quotaCost > quotaRecord.quotaLimit) {
                logger_1.logger.warn(`YouTube API quota would be exceeded. Used: ${quotaRecord.quotaUsed}, Cost: ${quotaCost}, Limit: ${quotaRecord.quotaLimit}`);
                throw (0, errorHandler_1.createError)('YouTube API daily quota exceeded', 429, shared_1.ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED);
            }
            // Update quota usage
            await database_1.prisma.youTubeApiQuota.update({
                where: { id: quotaRecord.id },
                data: {
                    quotaUsed: quotaRecord.quotaUsed + quotaCost,
                    searchCalls: quotaCost === shared_1.YOUTUBE_API.QUOTA_COSTS.SEARCH ?
                        quotaRecord.searchCalls + 1 : quotaRecord.searchCalls,
                    channelCalls: quotaCost === shared_1.YOUTUBE_API.QUOTA_COSTS.CHANNELS ?
                        quotaRecord.channelCalls + 1 : quotaRecord.channelCalls
                }
            });
            logger_1.logger.debug(`YouTube API quota updated: ${quotaRecord.quotaUsed + quotaCost}/${quotaRecord.quotaLimit}`);
        }
        catch (error) {
            if (error.code === shared_1.ErrorCode.YOUTUBE_API_QUOTA_EXCEEDED) {
                throw error;
            }
            logger_1.logger.error('Error updating YouTube API quota:', error);
            // Don't throw here to avoid blocking API calls due to quota tracking issues
        }
    }
    /**
     * Get current quota usage for today
     */
    async getQuotaUsage() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const quotaRecord = await database_1.prisma.youTubeApiQuota.findUnique({
            where: { date: today }
        });
        return {
            used: quotaRecord?.quotaUsed || 0,
            limit: quotaRecord?.quotaLimit || shared_1.YOUTUBE_API.DAILY_QUOTA_LIMIT,
            remaining: (quotaRecord?.quotaLimit || shared_1.YOUTUBE_API.DAILY_QUOTA_LIMIT) - (quotaRecord?.quotaUsed || 0),
            searchCalls: quotaRecord?.searchCalls || 0,
            channelCalls: quotaRecord?.channelCalls || 0
        };
    }
}
exports.YouTubeService = YouTubeService;
exports.youtubeService = new YouTubeService();
//# sourceMappingURL=youtubeService.js.map