# YouTube Content Management Platform

A modern web application for organizing, tracking, and analyzing YouTube channels and content, similar to Viral Snapper/VELIO.

## Features

- **Channel Organization**: Create custom lists/categories for organizing YouTube channels by topic
- **YouTube Data Integration**: Fetch channel information, subscriber counts, and video metadata using YouTube Data API v3
- **Content Discovery**: Search and filter YouTube channels based on various criteria
- **Dark-themed Dashboard**: Multiple view modes (grid, list, detailed) for managing saved channels
- **Activity Tracking**: Log user actions and changes
- **Real-time Updates**: Periodic refresh of channel statistics

## Technology Stack

### Frontend
- React.js with TypeScript
- Tailwind CSS for styling
- React Query for API state management
- Zustand for global state management
- React Router for navigation

### Backend
- Node.js with Express.js
- TypeScript
- PostgreSQL database
- Prisma ORM
- Redis for caching
- YouTube Data API v3

## Project Structure

```
├── frontend/          # React frontend application
├── backend/           # Node.js backend API
├── shared/            # Shared types and utilities
└── docs/              # Documentation
```

## Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL
- Redis (optional, for caching)
- YouTube Data API v3 key

### Installation

1. Clone the repository
2. Install dependencies for both frontend and backend
3. Set up environment variables
4. Run database migrations
5. Start the development servers

## Development

This project follows a three-tier architecture:
1. **Frontend**: React SPA for user interface
2. **Backend API**: Node.js API for business logic and YouTube integration
3. **Database**: PostgreSQL for data persistence

## API Quota Management

The application implements careful YouTube API quota management:
- Aggressive caching strategies
- Smart update scheduling
- Rate limiting protection
- Error handling for quota exceeded scenarios

## License

MIT License
