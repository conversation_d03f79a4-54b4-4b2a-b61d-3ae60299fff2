import { useState } from 'react'
import { Grid, List, MoreHorizontal, Plus } from 'lucide-react'
import { ChannelListCard } from '@/components/ChannelList/ChannelListCard'
import { ViewModeToggle } from '@/components/UI/ViewModeToggle'
import { CreateListModal } from '@/components/ChannelList/CreateListModal'

// Mock data - will be replaced with real data from API
const mockChannelLists = [
  {
    id: '1',
    name: 'YouTube Education (All)',
    description: 'Educational channels covering various topics',
    channelCount: 32,
    color: '#06B6D4',
    lastUpdated: '2 days ago',
    channels: [
      { id: '1', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Khan Academy' },
      { id: '2', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Crash Course' },
      { id: '3', thumbnailUrl: 'https://via.placeholder.com/40', title: 'TED-Ed' },
      { id: '4', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Veritasium' },
    ]
  },
  {
    id: '2',
    name: 'Business/Make Money Channels',
    description: 'Entrepreneurship and business growth content',
    channelCount: 18,
    color: '#10B981',
    lastUpdated: '1 week ago',
    channels: [
      { id: '5', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Gary Vaynerchuk' },
      { id: '6', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Ali Abdaal' },
      { id: '7', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Graham Stephan' },
    ]
  },
  {
    id: '3',
    name: 'Worlds Best Entertainment Creators',
    description: 'Top entertainment content from around the globe',
    channelCount: 24,
    color: '#EC4899',
    lastUpdated: 'yesterday',
    channels: [
      { id: '8', thumbnailUrl: 'https://via.placeholder.com/40', title: 'MrBeast' },
      { id: '9', thumbnailUrl: 'https://via.placeholder.com/40', title: 'PewDiePie' },
      { id: '10', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Dude Perfect' },
    ]
  },
  {
    id: '4',
    name: 'Worlds Best Documentary Channels',
    description: 'Curated documentary and informational content',
    channelCount: 15,
    color: '#F59E0B',
    lastUpdated: '5 days ago',
    channels: [
      { id: '11', thumbnailUrl: 'https://via.placeholder.com/40', title: 'National Geographic' },
      { id: '12', thumbnailUrl: 'https://via.placeholder.com/40', title: 'BBC Earth' },
      { id: '13', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Discovery' },
    ]
  },
  {
    id: '5',
    name: 'Make Money Channels',
    description: '',
    channelCount: 31,
    color: '#8B5CF6',
    lastUpdated: 'Today',
    featured: true,
    channels: [
      { id: '14', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Ryan Kaji' },
      { id: '15', thumbnailUrl: 'https://via.placeholder.com/40', title: 'Jeffree Star' },
    ]
  }
]

export function Dashboard() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showCreateModal, setShowCreateModal] = useState(false)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Saved Channels</h1>
          <p className="text-slate-400 mt-1">Manage and organize your favorite content creators</p>
        </div>
        
        <div className="flex items-center gap-4">
          <ViewModeToggle viewMode={viewMode} onViewModeChange={setViewMode} />
          <button 
            onClick={() => setShowCreateModal(true)}
            className="btn-primary btn-md"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create List
          </button>
        </div>
      </div>

      {/* Channel Lists Grid */}
      <div className={`grid gap-6 ${
        viewMode === 'grid' 
          ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
          : 'grid-cols-1'
      }`}>
        {mockChannelLists.map((list) => (
          <ChannelListCard
            key={list.id}
            list={list}
            viewMode={viewMode}
          />
        ))}
        
        {/* Add New Channel List Card */}
        <div 
          onClick={() => setShowCreateModal(true)}
          className="card border-2 border-dashed border-dark-600 hover:border-primary-500 transition-colors cursor-pointer group min-h-[200px] flex items-center justify-center"
        >
          <div className="text-center">
            <Plus className="w-8 h-8 text-slate-400 group-hover:text-primary-500 mx-auto mb-2" />
            <p className="text-slate-400 group-hover:text-primary-500 font-medium">
              Add New Channel List
            </p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center gap-3 text-sm">
            <div className="w-2 h-2 bg-success-500 rounded-full"></div>
            <span className="text-slate-300">
              Added <span className="text-white font-medium">MrBeast</span> to <span className="text-primary-400">Make Money Channels</span>
            </span>
            <span className="text-slate-500 ml-auto">Today at 2:45 PM</span>
          </div>
          <div className="flex items-center gap-3 text-sm">
            <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
            <span className="text-slate-300">
              Renamed list from "Finance" to <span className="text-primary-400">Make Money Channels</span>
            </span>
            <span className="text-slate-500 ml-auto">Yesterday at 5:12 PM</span>
          </div>
          <div className="flex items-center gap-3 text-sm">
            <div className="w-2 h-2 bg-success-500 rounded-full"></div>
            <span className="text-slate-300">
              Created new list <span className="text-primary-400">Make Money Channels</span>
            </span>
            <span className="text-slate-500 ml-auto">2 days ago</span>
          </div>
        </div>
      </div>

      {/* Create List Modal */}
      <CreateListModal 
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  )
}
