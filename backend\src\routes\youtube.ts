import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { youtubeApiLimiter } from '@/middleware/rateLimiter';
import { youtubeService } from '@/services/youtubeService';
import { YouTubeSearchFilters } from '@youtube-manager/shared';

const router = Router();

// Apply YouTube API rate limiting to all routes
router.use(youtubeApiLimiter);

/**
 * Search YouTube channels
 * POST /api/youtube/search
 */
router.post('/search', asyncHandler(async (req, res) => {
  const filters: YouTubeSearchFilters = req.body;
  const maxResults = parseInt(req.query.maxResults as string) || 25;

  const result = await youtubeService.searchChannels(filters, maxResults);

  res.json({
    success: true,
    data: result
  });
}));

/**
 * Get channel details by ID
 * GET /api/youtube/channel/:channelId
 */
router.get('/channel/:channelId', asyncHandler(async (req, res) => {
  const { channelId } = req.params;

  const channel = await youtubeService.getChannelById(channelId);

  if (!channel) {
    return res.status(404).json({
      success: false,
      error: 'Channel not found'
    });
  }

  res.json({
    success: true,
    data: channel
  });
}));

/**
 * Get channel statistics
 * GET /api/youtube/channel/:channelId/stats
 */
router.get('/channel/:channelId/stats', asyncHandler(async (req, res) => {
  const { channelId } = req.params;

  const stats = await youtubeService.getChannelStats(channelId);

  res.json({
    success: true,
    data: stats
  });
}));

/**
 * Get multiple channels by IDs
 * POST /api/youtube/channels
 */
router.post('/channels', asyncHandler(async (req, res) => {
  const { channelIds } = req.body;

  if (!Array.isArray(channelIds) || channelIds.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'channelIds array is required'
    });
  }

  const channels = await youtubeService.getChannelsByIds(channelIds);

  res.json({
    success: true,
    data: channels
  });
}));

/**
 * Get current API quota usage
 * GET /api/youtube/quota
 */
router.get('/quota', asyncHandler(async (req, res) => {
  const quotaUsage = await youtubeService.getQuotaUsage();

  res.json({
    success: true,
    data: quotaUsage
  });
}));

export default router;
