import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { youtubeApiLimiter } from '@/middleware/rateLimiter';
import { youtubeService } from '@/services/youtubeService';
import { YouTubeSearchFilters } from '@/types/shared';

const router = Router();

// Apply YouTube API rate limiting to all routes
router.use(youtubeApiLimiter);

/**
 * Configure YouTube API key
 * POST /api/youtube/configure
 */
router.post('/configure', asyncHandler(async (req, res) => {
  const { apiKey } = req.body;

  if (!apiKey || typeof apiKey !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'API key is required'
    });
  }

  if (!apiKey.startsWith('AIza')) {
    return res.status(400).json({
      success: false,
      error: 'Invalid API key format'
    });
  }

  try {
    // Test the API key by making a simple request
    const isValid = await youtubeService.testApiKey(apiKey);

    if (!isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid API key or API access denied'
      });
    }

    // Save to environment (in production, this would be saved to a secure config)
    process.env.YOUTUBE_API_KEY = apiKey;

    // Update the service with the new API key
    youtubeService.updateApiKey(apiKey);

    res.json({
      success: true,
      message: 'API key configured successfully'
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: 'Invalid API key or API access denied'
    });
  }
}));

/**
 * Get YouTube API status
 * GET /api/youtube/status
 */
router.get('/status', asyncHandler(async (req, res) => {
  const configured = !!process.env.YOUTUBE_API_KEY;
  const keyPreview = configured
    ? process.env.YOUTUBE_API_KEY!.substring(0, 8) + '...'
    : null;

  res.json({
    success: true,
    data: {
      configured,
      keyPreview
    }
  });
}));

/**
 * Search YouTube channels
 * GET /api/youtube/search
 */
router.get('/search', asyncHandler(async (req, res) => {
  const filters: YouTubeSearchFilters = {
    query: req.query.query as string,
    minSubscribers: req.query.minSubscribers ? parseInt(req.query.minSubscribers as string) : undefined,
    maxSubscribers: req.query.maxSubscribers ? parseInt(req.query.maxSubscribers as string) : undefined,
    order: req.query.order as any || 'relevance'
  };

  const maxResults = parseInt(req.query.limit as string) || 25;

  if (!filters.query) {
    return res.status(400).json({
      success: false,
      error: 'Search query is required'
    });
  }

  const result = await youtubeService.searchChannels(filters, maxResults);

  res.json({
    success: true,
    data: result
  });
}));

/**
 * Get channel details by ID
 * GET /api/youtube/channel/:channelId
 */
router.get('/channel/:channelId', asyncHandler(async (req, res) => {
  const { channelId } = req.params;

  const channel = await youtubeService.getChannelById(channelId);

  if (!channel) {
    return res.status(404).json({
      success: false,
      error: 'Channel not found'
    });
  }

  res.json({
    success: true,
    data: channel
  });
}));

/**
 * Get channel statistics
 * GET /api/youtube/channel/:channelId/stats
 */
router.get('/channel/:channelId/stats', asyncHandler(async (req, res) => {
  const { channelId } = req.params;

  const stats = await youtubeService.getChannelStats(channelId);

  res.json({
    success: true,
    data: stats
  });
}));

/**
 * Get multiple channels by IDs
 * POST /api/youtube/channels
 */
router.post('/channels', asyncHandler(async (req, res) => {
  const { channelIds } = req.body;

  if (!Array.isArray(channelIds) || channelIds.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'channelIds array is required'
    });
  }

  const channels = await youtubeService.getChannelsByIds(channelIds);

  res.json({
    success: true,
    data: channels
  });
}));

/**
 * Get current API quota usage
 * GET /api/youtube/quota
 */
router.get('/quota', asyncHandler(async (req, res) => {
  const quotaUsage = await youtubeService.getQuotaUsage();

  res.json({
    success: true,
    data: quotaUsage
  });
}));

export default router;
