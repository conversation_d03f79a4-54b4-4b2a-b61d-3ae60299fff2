import { useYouTubeQuota } from '@/hooks/useYouTubeSearch'
import { useQuotaMonitoring } from '@/hooks/useRealtime'
import { AlertTriangle, Zap } from 'lucide-react'

export function QuotaIndicator() {
  const { data: quotaData } = useYouTubeQuota()
  const realtimeQuota = useQuotaMonitoring()

  // Use real-time data if available, otherwise fall back to API data
  const quota = realtimeQuota || quotaData

  if (!quota) {
    return null
  }

  const percentage = (quota.current / quota.limit) * 100
  const isWarning = percentage > 80
  const isCritical = percentage > 95

  const getStatusColor = () => {
    if (isCritical) return 'text-red-400'
    if (isWarning) return 'text-yellow-400'
    return 'text-green-400'
  }

  const getBarColor = () => {
    if (isCritical) return 'bg-red-500'
    if (isWarning) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="flex items-center gap-2 px-3 py-2 bg-dark-700 rounded-lg">
      <div className="flex items-center gap-1">
        {isWarning ? (
          <AlertTriangle className={`w-4 h-4 ${getStatusColor()}`} />
        ) : (
          <Zap className={`w-4 h-4 ${getStatusColor()}`} />
        )}
        <span className="text-xs font-medium text-slate-300">API Quota</span>
      </div>
      
      <div className="flex items-center gap-2">
        <div className="w-16 h-2 bg-dark-600 rounded-full overflow-hidden">
          <div 
            className={`h-full transition-all duration-300 ${getBarColor()}`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
        
        <span className={`text-xs font-mono ${getStatusColor()}`}>
          {quota.current.toLocaleString()}/{quota.limit.toLocaleString()}
        </span>
      </div>
      
      {isWarning && (
        <div className="text-xs text-slate-400">
          ({percentage.toFixed(1)}%)
        </div>
      )}
    </div>
  )
}
