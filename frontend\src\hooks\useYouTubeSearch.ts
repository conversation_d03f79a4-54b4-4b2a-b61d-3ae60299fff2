import { useQuery } from '@tanstack/react-query'
import { youtubeApi } from '@/services/api'
import { YouTubeSearchFilters } from '@youtube-manager/shared'

export function useYouTubeSearch(
  filters: YouTubeSearchFilters,
  maxResults: number = 25,
  enabled: boolean = false
) {
  return useQuery({
    queryKey: ['youtube-search', filters, maxResults],
    queryFn: () => youtubeApi.searchChannels(filters, maxResults),
    enabled: enabled && (!!filters.query || Object.keys(filters).length > 1),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on quota exceeded errors
      if (error?.response?.status === 429) {
        return false
      }
      return failureCount < 2
    }
  })
}

export function useYouTubeChannel(channelId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['youtube-channel', channelId],
    queryFn: () => youtubeApi.getChannel(channelId),
    enabled: enabled && !!channelId,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  })
}

export function useYouTubeChannelStats(channelId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['youtube-channel-stats', channelId],
    queryFn: () => youtubeApi.getChannelStats(channelId),
    enabled: enabled && !!channelId,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}

export function useYouTubeQuota() {
  return useQuery({
    queryKey: ['youtube-quota'],
    queryFn: () => youtubeApi.getQuotaUsage(),
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}
