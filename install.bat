@echo off
setlocal enabledelayedexpansion

echo 🚀 YouTube Content Manager - Installation Script
echo =================================================

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    echo    Visit: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js detected

REM Check if npm is installed
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm is not installed.
    pause
    exit /b 1
)

echo ✅ npm detected

echo.
echo 📦 Installing dependencies...
echo ==============================

REM Install root dependencies
echo Installing root dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install root dependencies
    pause
    exit /b 1
)

REM Install shared package dependencies
echo Installing shared package dependencies...
cd shared
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install shared dependencies
    pause
    exit /b 1
)
cd ..

REM Install backend dependencies
echo Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

REM Install frontend dependencies
echo Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo 🔨 Building shared package...
echo ============================
cd shared
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build shared package
    pause
    exit /b 1
)
cd ..

echo.
echo ⚙️  Setting up environment files...
echo ==================================

REM Backend environment
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    echo ✅ Created backend/.env from example
    echo ⚠️  Please edit backend/.env with your configuration
) else (
    echo ✅ backend/.env already exists
)

REM Frontend environment
if not exist "frontend\.env" (
    copy "frontend\.env.example" "frontend\.env" >nul
    echo ✅ Created frontend/.env from example
) else (
    echo ✅ frontend/.env already exists
)

echo.
echo 🎉 Installation completed successfully!
echo ======================================
echo.
echo Next steps:
echo 1. Set up PostgreSQL database
echo 2. Get YouTube Data API key from Google Cloud Console
echo 3. Edit backend/.env with your database URL and API key
echo 4. Run database migrations: cd backend ^&^& npm run db:push
echo 5. Start the development servers: npm run dev
echo.
echo For detailed setup instructions, see setup.md
echo.
echo Happy coding! 🚀
echo.
pause
