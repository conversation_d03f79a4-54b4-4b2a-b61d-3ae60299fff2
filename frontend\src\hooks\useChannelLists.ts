import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { channelListsApi } from '@/services/api'
import { useAppStore } from '@/store/useAppStore'
import { toast } from 'react-hot-toast'

export function useChannelLists() {
  const setChannelLists = useAppStore((state) => state.setChannelLists)
  
  return useQuery({
    queryKey: ['channel-lists'],
    queryFn: async () => {
      const lists = await channelListsApi.getAll()
      setChannelLists(lists)
      return lists
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useChannelList(listId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['channel-list', listId],
    queryFn: () => channelListsApi.getById(listId),
    enabled: enabled && !!listId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useCreateChannelList() {
  const queryClient = useQueryClient()
  const addChannelList = useAppStore((state) => state.addChannelList)

  return useMutation({
    mutationFn: channelListsApi.create,
    onSuccess: (newList) => {
      // Update the cache
      queryClient.invalidateQueries({ queryKey: ['channel-lists'] })
      addChannelList(newList)
      toast.success('Channel list created successfully!')
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.error || 'Failed to create channel list')
    }
  })
}

export function useUpdateChannelList() {
  const queryClient = useQueryClient()
  const updateChannelList = useAppStore((state) => state.updateChannelList)

  return useMutation({
    mutationFn: ({ listId, data }: { listId: string; data: any }) =>
      channelListsApi.update(listId, data),
    onSuccess: (updatedList) => {
      // Update the cache
      queryClient.invalidateQueries({ queryKey: ['channel-lists'] })
      queryClient.invalidateQueries({ queryKey: ['channel-list', updatedList.id] })
      updateChannelList(updatedList.id, updatedList)
      toast.success('Channel list updated successfully!')
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.error || 'Failed to update channel list')
    }
  })
}

export function useDeleteChannelList() {
  const queryClient = useQueryClient()
  const removeChannelList = useAppStore((state) => state.removeChannelList)

  return useMutation({
    mutationFn: channelListsApi.delete,
    onSuccess: (_, listId) => {
      // Update the cache
      queryClient.invalidateQueries({ queryKey: ['channel-lists'] })
      removeChannelList(listId)
      toast.success('Channel list deleted successfully!')
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.error || 'Failed to delete channel list')
    }
  })
}

export function useAddChannelToList() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ listId, channelId }: { listId: string; channelId: string }) =>
      channelListsApi.addChannel(listId, channelId),
    onSuccess: (_, { listId }) => {
      // Update the cache
      queryClient.invalidateQueries({ queryKey: ['channel-lists'] })
      queryClient.invalidateQueries({ queryKey: ['channel-list', listId] })
      toast.success('Channel added to list successfully!')
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.error || 'Failed to add channel to list')
    }
  })
}

export function useRemoveChannelFromList() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ listId, channelId }: { listId: string; channelId: string }) =>
      channelListsApi.removeChannel(listId, channelId),
    onSuccess: (_, { listId }) => {
      // Update the cache
      queryClient.invalidateQueries({ queryKey: ['channel-lists'] })
      queryClient.invalidateQueries({ queryKey: ['channel-list', listId] })
      toast.success('Channel removed from list successfully!')
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.error || 'Failed to remove channel from list')
    }
  })
}
