import { createClient, RedisClientType } from 'redis';
import { logger } from './logger';

let redisClient: RedisClientType | null = null;

export async function connectRedis(): Promise<RedisClientType | null> {
  if (!process.env.REDIS_URL) {
    logger.info('Redis URL not provided, skipping Redis connection');
    return null;
  }

  try {
    redisClient = createClient({
      url: process.env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            logger.error('Redis reconnection failed after 10 attempts');
            return false;
          }
          return Math.min(retries * 100, 3000);
        }
      }
    });

    redisClient.on('error', (err) => {
      logger.error('Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      logger.info('Redis client ready');
    });

    redisClient.on('end', () => {
      logger.info('Redis client disconnected');
    });

    await redisClient.connect();
    return redisClient;
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    redisClient = null;
    throw error;
  }
}

export function getRedisClient(): RedisClientType | null {
  return redisClient;
}

export async function setCache(key: string, value: any, ttlSeconds?: number): Promise<void> {
  if (!redisClient) {
    logger.debug('Redis not available, skipping cache set');
    return;
  }

  try {
    const serializedValue = JSON.stringify(value);
    if (ttlSeconds) {
      await redisClient.setEx(key, ttlSeconds, serializedValue);
    } else {
      await redisClient.set(key, serializedValue);
    }
    logger.debug(`Cache set for key: ${key}`);
  } catch (error) {
    logger.error('Error setting cache:', error);
  }
}

export async function getCache<T>(key: string): Promise<T | null> {
  if (!redisClient) {
    logger.debug('Redis not available, skipping cache get');
    return null;
  }

  try {
    const value = await redisClient.get(key);
    if (value) {
      logger.debug(`Cache hit for key: ${key}`);
      return JSON.parse(value) as T;
    }
    logger.debug(`Cache miss for key: ${key}`);
    return null;
  } catch (error) {
    logger.error('Error getting cache:', error);
    return null;
  }
}

export async function deleteCache(key: string): Promise<void> {
  if (!redisClient) {
    logger.debug('Redis not available, skipping cache delete');
    return;
  }

  try {
    await redisClient.del(key);
    logger.debug(`Cache deleted for key: ${key}`);
  } catch (error) {
    logger.error('Error deleting cache:', error);
  }
}

export async function deleteCachePattern(pattern: string): Promise<void> {
  if (!redisClient) {
    logger.debug('Redis not available, skipping cache pattern delete');
    return;
  }

  try {
    const keys = await redisClient.keys(pattern);
    if (keys.length > 0) {
      await redisClient.del(keys);
      logger.debug(`Cache deleted for pattern: ${pattern}, keys: ${keys.length}`);
    }
  } catch (error) {
    logger.error('Error deleting cache pattern:', error);
  }
}

export async function disconnectRedis(): Promise<void> {
  if (redisClient) {
    try {
      await redisClient.quit();
      logger.info('Redis connection closed');
    } catch (error) {
      logger.error('Error closing Redis connection:', error);
    }
  }
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await disconnectRedis();
});
