import { useState } from 'react'
import { Search as SearchIcon, Filter, SlidersHorizontal } from 'lucide-react'
import { SearchFilters } from '@/components/Search/SearchFilters'
import { SearchResults } from '@/components/Search/SearchResults'
import { YouTubeSearchFilters } from '@youtube-manager/shared'

export function Search() {
  const [query, setQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<YouTubeSearchFilters>({})
  const [isSearching, setIsSearching] = useState(false)

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!query.trim()) return

    setIsSearching(true)
    // TODO: Implement search logic
    console.log('Searching for:', query, 'with filters:', filters)
    
    // Simulate API call
    setTimeout(() => {
      setIsSearching(false)
    }, 2000)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Search Channels</h1>
        <p className="text-slate-400 mt-1">Discover new YouTube channels and content creators</p>
      </div>

      {/* Search Form */}
      <div className="card">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search for channels, topics, or keywords..."
                className="input w-full pl-10"
              />
            </div>
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className={`btn-secondary btn-md ${showFilters ? 'bg-primary-600 text-white' : ''}`}
            >
              <SlidersHorizontal className="w-4 h-4 mr-2" />
              Filters
            </button>
            <button
              type="submit"
              disabled={!query.trim() || isSearching}
              className="btn-primary btn-md min-w-[100px]"
            >
              {isSearching ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                'Search'
              )}
            </button>
          </div>

          {/* Search Filters */}
          {showFilters && (
            <SearchFilters
              filters={filters}
              onFiltersChange={setFilters}
            />
          )}
        </form>
      </div>

      {/* Search Results */}
      <SearchResults
        query={query}
        filters={filters}
        isSearching={isSearching}
      />
    </div>
  )
}
