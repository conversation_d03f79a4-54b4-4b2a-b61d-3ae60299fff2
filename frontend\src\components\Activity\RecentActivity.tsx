import { ActivityType } from '@youtube-manager/shared'
import { formatTimeAgo } from '@youtube-manager/shared'
import { useRecentActivity } from '@/hooks/useActivity'

const activityColors = {
  [ActivityType.CHANNEL_ADDED]: 'bg-success-500',
  [ActivityType.CHANNEL_REMOVED]: 'bg-red-500',
  [ActivityType.LIST_CREATED]: 'bg-primary-500',
  [ActivityType.LIST_UPDATED]: 'bg-blue-500',
  [ActivityType.LIST_DELETED]: 'bg-red-500',
  [ActivityType.LIST_RENAMED]: 'bg-yellow-500',
  [ActivityType.CHANNEL_MOVED]: 'bg-purple-500'
}

export function RecentActivity() {
  const { data: activities = [], isLoading } = useRecentActivity(10)

  if (isLoading) {
    return (
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex items-center gap-3 animate-pulse">
              <div className="w-2 h-2 bg-dark-600 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-dark-600 rounded w-3/4 mb-1"></div>
                <div className="h-3 bg-dark-700 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (activities.length === 0) {
    return (
      <div className="card">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
        <div className="text-center py-8">
          <div className="text-slate-400 mb-2">
            <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm">No recent activity</p>
            <p className="text-xs text-slate-500 mt-1">Start organizing channels to see your activity here</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
      <div className="space-y-3">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-center gap-3 text-sm">
            <div className={`w-2 h-2 rounded-full ${activityColors[activity.type] || 'bg-slate-500'}`}></div>
            <span className="text-slate-300 flex-1">
              {renderActivityDescription(activity.description, activity.metadata)}
            </span>
            <span className="text-slate-500 text-xs">
              {formatTimeAgo(new Date(activity.createdAt))}
            </span>
          </div>
        ))}
      </div>
      
      {activities.length >= 10 && (
        <div className="mt-4 pt-4 border-t border-dark-600">
          <button className="text-primary-400 hover:text-primary-300 text-sm font-medium">
            View all activity
          </button>
        </div>
      )}
    </div>
  )
}

function renderActivityDescription(description: string, metadata: any) {
  // Parse the description to highlight important parts
  const parts = description.split(/("[^"]*")/g)
  
  return (
    <span>
      {parts.map((part, index) => {
        if (part.startsWith('"') && part.endsWith('"')) {
          // This is a quoted part - highlight it
          const text = part.slice(1, -1) // Remove quotes
          return (
            <span key={index} className="text-white font-medium">
              {text}
            </span>
          )
        }
        return part
      })}
    </span>
  )
}
