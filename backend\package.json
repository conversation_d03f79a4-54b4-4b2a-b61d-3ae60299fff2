{"name": "@youtube-manager/backend", "version": "1.0.0", "description": "Backend API for YouTube Content Manager", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"@youtube-manager/shared": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "zod": "^3.22.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "googleapis": "^128.0.0", "@prisma/client": "^5.7.1", "redis": "^4.6.11", "node-cron": "^3.0.3", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/socket.io": "^3.0.2", "typescript": "^5.3.3", "tsx": "^4.6.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prisma": "^5.7.1", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}