// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  channelLists ChannelList[]
  activities   ActivityLog[]

  @@map("users")
}

model YouTubeChannel {
  id             String   @id @default(cuid())
  channelId      String   @unique // YouTube channel ID
  title          String
  description    String   @default("")
  thumbnailUrl   String
  subscriberCount Int     @default(0)
  videoCount     Int      @default(0)
  viewCount      BigInt   @default(0)
  publishedAt    DateTime
  customUrl      String?
  country        String?
  lastUpdated    DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  channelListItems ChannelListItem[]

  @@map("youtube_channels")
}

model ChannelList {
  id           String   @id @default(cuid())
  userId       String
  name         String
  description  String?
  color        String   @default("#8B5CF6")
  isPublic     Boolean  @default(false)
  channelCount Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user         User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  channelItems ChannelListItem[]

  @@map("channel_lists")
}

model ChannelListItem {
  id            String   @id @default(cuid())
  channelListId String
  channelId     String
  addedAt       DateTime @default(now())

  // Relations
  channelList ChannelList    @relation(fields: [channelListId], references: [id], onDelete: Cascade)
  channel     YouTubeChannel @relation(fields: [channelId], references: [id], onDelete: Cascade)

  @@unique([channelListId, channelId])
  @@map("channel_list_items")
}

enum ActivityType {
  CHANNEL_ADDED
  CHANNEL_REMOVED
  LIST_CREATED
  LIST_UPDATED
  LIST_DELETED
  LIST_RENAMED
  CHANNEL_MOVED
}

model ActivityLog {
  id          String       @id @default(cuid())
  userId      String
  type        ActivityType
  description String
  metadata    Json?
  createdAt   DateTime     @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("activity_logs")
}

model YouTubeApiQuota {
  id           String   @id @default(cuid())
  date         DateTime @unique @default(now())
  quotaUsed    Int      @default(0)
  quotaLimit   Int      @default(10000)
  searchCalls  Int      @default(0)
  channelCalls Int      @default(0)
  videoCalls   Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("youtube_api_quota")
}
