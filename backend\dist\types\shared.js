"use strict";
// Shared types for the YouTube Content Manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.CACHE_TTL = exports.CACHE_KEYS = exports.YOUTUBE_API = exports.ErrorCode = exports.ActivityType = void 0;
// Activity Log types
var ActivityType;
(function (ActivityType) {
    ActivityType["CHANNEL_ADDED"] = "CHANNEL_ADDED";
    ActivityType["CHANNEL_REMOVED"] = "CHANNEL_REMOVED";
    ActivityType["LIST_CREATED"] = "LIST_CREATED";
    ActivityType["LIST_UPDATED"] = "LIST_UPDATED";
    ActivityType["LIST_DELETED"] = "LIST_DELETED";
    ActivityType["LIST_RENAMED"] = "LIST_RENAMED";
    ActivityType["CHANNEL_MOVED"] = "CHANNEL_MOVED";
})(ActivityType || (exports.ActivityType = ActivityType = {}));
var ErrorCode;
(function (ErrorCode) {
    ErrorCode["YOUTUBE_API_QUOTA_EXCEEDED"] = "YOUTUBE_API_QUOTA_EXCEEDED";
    ErrorCode["YOUTUBE_API_ERROR"] = "YOUTUBE_API_ERROR";
    ErrorCode["CHANNEL_NOT_FOUND"] = "CHANNEL_NOT_FOUND";
    ErrorCode["LIST_NOT_FOUND"] = "LIST_NOT_FOUND";
    ErrorCode["UNAUTHORIZED"] = "UNAUTHORIZED";
    ErrorCode["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorCode["DATABASE_ERROR"] = "DATABASE_ERROR";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
// Constants
exports.YOUTUBE_API = {
    BASE_URL: 'https://www.googleapis.com/youtube/v3',
    QUOTA_COSTS: {
        SEARCH: 100,
        CHANNELS: 1,
        VIDEOS: 1,
        PLAYLISTS: 1
    },
    DAILY_QUOTA_LIMIT: 10000,
    MAX_RESULTS_PER_REQUEST: 50
};
exports.CACHE_KEYS = {
    YOUTUBE_CHANNEL: 'youtube:channel:',
    YOUTUBE_SEARCH: 'youtube:search:',
    CHANNEL_LISTS: 'channel:lists:',
    USER_ACTIVITY: 'user:activity:'
};
exports.CACHE_TTL = {
    YOUTUBE_CHANNEL: 3600, // 1 hour
    YOUTUBE_SEARCH: 1800, // 30 minutes
    CHANNEL_LISTS: 300, // 5 minutes
    USER_ACTIVITY: 600 // 10 minutes
};
//# sourceMappingURL=shared.js.map