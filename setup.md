# YouTube Content Manager - Setup Guide

This guide will help you set up and run the YouTube Content Management Platform locally.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** (version 8 or higher)
- **PostgreSQL** (version 12 or higher)
- **Redis** (optional, for caching)

## Getting Started

### 1. Install Dependencies

First, install all dependencies for the monorepo:

```bash
npm run install:all
```

This will install dependencies for the root, frontend, backend, and shared packages.

### 2. Set Up Environment Variables

#### Backend Environment Variables

Copy the example environment file and configure it:

```bash
cd backend
cp .env.example .env
```

Edit `backend/.env` with your configuration:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/youtube_manager"

# Redis (optional, for caching)
REDIS_URL="redis://localhost:6379"

# YouTube Data API v3
YOUTUBE_API_KEY="your_youtube_api_key_here"

# JWT Secret
JWT_SECRET="your_super_secret_jwt_key_here"

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL="http://localhost:3000"
```

#### Frontend Environment Variables

```bash
cd frontend
cp .env.example .env
```

Edit `frontend/.env`:

```env
VITE_API_URL=http://localhost:3001
VITE_APP_NAME="YouTube Content Manager"
VITE_APP_VERSION=1.0.0
```

### 3. Get YouTube Data API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the YouTube Data API v3
4. Create credentials (API Key)
5. Copy the API key to your backend `.env` file

### 4. Set Up Database

#### Create PostgreSQL Database

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE youtube_manager;

# Create user (optional)
CREATE USER youtube_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE youtube_manager TO youtube_user;
```

#### Run Database Migrations

```bash
cd backend
npm run db:generate
npm run db:push
```

### 5. Set Up Redis (Optional)

If you want to use Redis for caching:

#### Install Redis

**macOS (using Homebrew):**
```bash
brew install redis
brew services start redis
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
```

**Windows:**
Download and install from [Redis Windows releases](https://github.com/microsoftarchive/redis/releases)

### 6. Build Shared Package

```bash
cd shared
npm run build
```

## Running the Application

### Development Mode

You can run both frontend and backend simultaneously:

```bash
# From the root directory
npm run dev
```

Or run them separately:

```bash
# Backend (Terminal 1)
cd backend
npm run dev

# Frontend (Terminal 2)
cd frontend
npm run dev
```

### Production Mode

```bash
# Build all packages
npm run build

# Start backend
cd backend
npm start

# Frontend will be served as static files
```

## Accessing the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Health Check**: http://localhost:3001/health

## Database Management

### Prisma Studio (Database GUI)

```bash
cd backend
npm run db:studio
```

### Reset Database

```bash
cd backend
npm run db:push --force-reset
```

### Seed Database (Optional)

```bash
cd backend
npm run db:seed
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Change the PORT in backend/.env
   - Or kill the process using the port: `lsof -ti:3001 | xargs kill -9`

2. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in backend/.env
   - Verify database exists and user has permissions

3. **YouTube API Quota Exceeded**
   - Check your quota usage in Google Cloud Console
   - Implement caching to reduce API calls
   - Consider upgrading your quota limit

4. **Redis Connection Error**
   - Redis is optional; the app will work without it
   - Ensure Redis is running if REDIS_URL is set
   - Remove REDIS_URL from .env to disable caching

### Logs

Backend logs are stored in:
- `backend/logs/combined.log` - All logs
- `backend/logs/error.log` - Error logs only

## Development Tips

### Code Quality

```bash
# Lint all packages
npm run lint

# Type check all packages
npm run type-check

# Fix linting issues
npm run lint:fix
```

### Database Schema Changes

When you modify the Prisma schema:

```bash
cd backend
npm run db:generate  # Generate Prisma client
npm run db:migrate   # Create and apply migration
```

### API Testing

Use tools like:
- **Postman** or **Insomnia** for API testing
- **curl** for quick tests
- Built-in health check: `curl http://localhost:3001/health`

## Next Steps

1. **Configure YouTube API Key** - Essential for channel search functionality
2. **Set up authentication** - Currently placeholder endpoints
3. **Implement real-time updates** - Background jobs for channel statistics
4. **Add more search filters** - Extend the search functionality
5. **Deploy to production** - Use services like Vercel, Railway, or AWS

## Support

If you encounter issues:

1. Check the logs in `backend/logs/`
2. Verify all environment variables are set correctly
3. Ensure all services (PostgreSQL, Redis) are running
4. Check the GitHub issues for similar problems

## Architecture Overview

```
├── frontend/          # React + TypeScript + Tailwind CSS
├── backend/           # Node.js + Express + Prisma + PostgreSQL
├── shared/            # Shared types and utilities
└── docs/              # Documentation
```

The application follows a three-tier architecture with proper separation of concerns, API quota management, and modern development practices.
