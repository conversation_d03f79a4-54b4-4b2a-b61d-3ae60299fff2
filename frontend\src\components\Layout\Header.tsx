import { Search, Bell, User, Plus } from 'lucide-react'

export function Header() {
  return (
    <header className="h-16 bg-dark-800 border-b border-dark-700 flex items-center justify-between px-6">
      {/* Search Bar */}
      <div className="flex-1 max-w-xl">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
          <input
            type="text"
            placeholder="Search videos, channels..."
            className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-sm text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center gap-4">
        {/* Create List Button */}
        <button className="btn-primary btn-sm">
          <Plus className="w-4 h-4 mr-2" />
          Create List
        </button>

        {/* Notifications */}
        <button className="p-2 text-slate-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors">
          <Bell className="w-5 h-5" />
        </button>

        {/* User Menu */}
        <button className="flex items-center gap-2 p-2 text-slate-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors">
          <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
          <span className="text-sm font-medium">Marcus R.</span>
        </button>
      </div>
    </header>
  )
}
