# YouTube Content Manager - Deployment Guide

This guide covers testing, building, and deploying the YouTube Content Management Platform to production.

## 🧪 Testing

### Backend Testing

```bash
cd backend

# Run type checking
npm run type-check

# Run linting
npm run lint

# Test database connection
npm run db:push

# Test API endpoints
curl http://localhost:3001/health
curl http://localhost:3001/api/youtube/quota
```

### Frontend Testing

```bash
cd frontend

# Run type checking
npm run type-check

# Run linting
npm run lint

# Build test
npm run build
```

### Integration Testing

1. **Start all services:**
   ```bash
   npm run dev
   ```

2. **Test core functionality:**
   - Channel search and filtering
   - Channel list creation and management
   - Real-time updates (open multiple browser tabs)
   - Activity logging
   - API quota monitoring

3. **Test error scenarios:**
   - Invalid YouTube API key
   - Database connection failure
   - Redis unavailable (should gracefully degrade)
   - API quota exceeded

## 🏗️ Building for Production

### 1. Environment Setup

Create production environment files:

**Backend (.env.production):**
```env
DATABASE_URL="********************************************/youtube_manager"
REDIS_URL="redis://your-redis-host:6379"
YOUTUBE_API_KEY="your_production_api_key"
JWT_SECRET="your_super_secure_jwt_secret"
PORT=3001
NODE_ENV=production
FRONTEND_URL="https://your-domain.com"
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOG_LEVEL=info
ENABLE_BACKGROUND_JOBS=true
CHANNEL_UPDATE_INTERVAL="0 */6 * * *"
```

**Frontend (.env.production):**
```env
VITE_API_URL=https://api.your-domain.com
VITE_APP_NAME="YouTube Content Manager"
VITE_APP_VERSION=1.0.0
```

### 2. Build Process

```bash
# Install dependencies
npm run install:all

# Build shared package
cd shared && npm run build && cd ..

# Build backend
cd backend && npm run build && cd ..

# Build frontend
cd frontend && npm run build && cd ..
```

## 🚀 Deployment Options

### Option 1: Traditional VPS/Server

#### Requirements
- Node.js 18+
- PostgreSQL 12+
- Redis (optional but recommended)
- Nginx (reverse proxy)
- PM2 (process management)

#### Setup Steps

1. **Install dependencies on server:**
   ```bash
   # Install Node.js, PostgreSQL, Redis, Nginx
   sudo apt update
   sudo apt install nodejs npm postgresql redis-server nginx
   
   # Install PM2 globally
   sudo npm install -g pm2
   ```

2. **Database setup:**
   ```bash
   sudo -u postgres createdb youtube_manager
   sudo -u postgres createuser youtube_user
   sudo -u postgres psql -c "ALTER USER youtube_user WITH PASSWORD 'secure_password';"
   sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE youtube_manager TO youtube_user;"
   ```

3. **Deploy application:**
   ```bash
   # Clone and build
   git clone your-repo
   cd youtube-content-manager
   npm run install:all
   npm run build
   
   # Run database migrations
   cd backend && npm run db:push
   ```

4. **PM2 configuration (ecosystem.config.js):**
   ```javascript
   module.exports = {
     apps: [{
       name: 'youtube-manager-backend',
       script: './backend/dist/index.js',
       instances: 2,
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 3001
       }
     }]
   }
   ```

5. **Nginx configuration:**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       # Frontend
       location / {
           root /path/to/frontend/dist;
           try_files $uri $uri/ /index.html;
       }
       
       # Backend API
       location /api {
           proxy_pass http://localhost:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
       
       # WebSocket for real-time updates
       location /socket.io {
           proxy_pass http://localhost:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
       }
   }
   ```

### Option 2: Docker Deployment

#### Docker Compose Setup

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: youtube_manager
      POSTGRES_USER: youtube_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    environment:
      DATABASE_URL: *******************************************************/youtube_manager
      REDIS_URL: redis://redis:6379
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
      JWT_SECRET: ${JWT_SECRET}
      NODE_ENV: production
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
```

**Backend Dockerfile:**
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY shared/ ./shared/
COPY backend/ ./backend/

# Build shared package
RUN cd shared && npm run build

# Build backend
RUN cd backend && npm run build

EXPOSE 3001

CMD ["node", "backend/dist/index.js"]
```

### Option 3: Cloud Platform Deployment

#### Vercel + Railway

1. **Frontend (Vercel):**
   - Connect GitHub repository
   - Set build command: `cd frontend && npm run build`
   - Set output directory: `frontend/dist`
   - Add environment variables

2. **Backend (Railway):**
   - Connect GitHub repository
   - Add PostgreSQL and Redis services
   - Set start command: `cd backend && npm start`
   - Configure environment variables

#### AWS/GCP/Azure

- Use container services (ECS, Cloud Run, Container Instances)
- Set up managed databases (RDS, Cloud SQL, Azure Database)
- Configure load balancers and CDN
- Set up monitoring and logging

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Backend health
curl https://api.your-domain.com/health

# Database connectivity
curl https://api.your-domain.com/api/youtube/quota

# Real-time connectivity
# Check WebSocket connection in browser dev tools
```

### Logging

- Backend logs: `backend/logs/`
- PM2 logs: `pm2 logs`
- Nginx logs: `/var/log/nginx/`

### Monitoring Setup

1. **Application monitoring:**
   - Set up error tracking (Sentry)
   - Monitor API response times
   - Track YouTube API quota usage

2. **Infrastructure monitoring:**
   - Server resources (CPU, memory, disk)
   - Database performance
   - Redis memory usage

3. **Alerts:**
   - API quota approaching limit
   - High error rates
   - Server downtime

### Backup Strategy

1. **Database backups:**
   ```bash
   # Daily automated backup
   pg_dump youtube_manager > backup_$(date +%Y%m%d).sql
   ```

2. **Configuration backups:**
   - Environment files
   - Nginx configuration
   - PM2 ecosystem file

### Updates and Maintenance

1. **Regular updates:**
   ```bash
   # Pull latest code
   git pull origin main
   
   # Install dependencies
   npm run install:all
   
   # Build application
   npm run build
   
   # Run migrations
   cd backend && npm run db:migrate
   
   # Restart services
   pm2 restart all
   ```

2. **Security updates:**
   - Keep Node.js and dependencies updated
   - Regular security audits: `npm audit`
   - Monitor for vulnerabilities

## 🔒 Security Considerations

### Production Security Checklist

- [ ] Use HTTPS/SSL certificates
- [ ] Secure database credentials
- [ ] Enable firewall rules
- [ ] Set up rate limiting
- [ ] Configure CORS properly
- [ ] Use secure JWT secrets
- [ ] Enable security headers (Helmet.js)
- [ ] Regular security updates
- [ ] Monitor for suspicious activity
- [ ] Backup encryption

### Environment Variables Security

- Never commit `.env` files
- Use secure secret management
- Rotate API keys regularly
- Limit API key permissions

## 📈 Performance Optimization

### Backend Optimization

- Enable Redis caching
- Optimize database queries
- Use connection pooling
- Enable gzip compression
- Set up CDN for static assets

### Frontend Optimization

- Code splitting and lazy loading
- Image optimization
- Bundle size analysis
- Service worker for caching
- Performance monitoring

## 🆘 Troubleshooting

### Common Issues

1. **Database connection errors:**
   - Check connection string
   - Verify database is running
   - Check firewall rules

2. **YouTube API errors:**
   - Verify API key is valid
   - Check quota limits
   - Monitor rate limiting

3. **Real-time connection issues:**
   - Check WebSocket proxy configuration
   - Verify CORS settings
   - Monitor connection logs

4. **High memory usage:**
   - Check for memory leaks
   - Monitor cache size
   - Optimize queries

### Debug Commands

```bash
# Check service status
pm2 status
systemctl status nginx
systemctl status postgresql
systemctl status redis

# View logs
pm2 logs
tail -f /var/log/nginx/error.log
tail -f backend/logs/error.log

# Database queries
psql -U youtube_user -d youtube_manager -c "SELECT COUNT(*) FROM youtube_channels;"

# Redis status
redis-cli info memory
```

This deployment guide provides comprehensive instructions for getting your YouTube Content Management Platform running in production with proper monitoring, security, and maintenance procedures.
